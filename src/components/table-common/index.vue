<template>
  <div class="tableStyleClass">
    <el-table :data="tableData" v-loading="isLoading" size="mini" :height="height" :empty-text="emptyText"
      :span-method="spanMethod" tooltip-effect="dark" @select="selectionSelect" @select-all="selectionSelectAll"
      @sort-change="sortChange" @row-click="rowClick" ref="tableRef" border stripe style="width: 100%">
      <el-table-column v-if="selection" type="selection" align="center" header-align="center" width="40">
      </el-table-column>
      <el-table-column type="index" fixed label="序号" align="center" header-align="center" width="60">
      </el-table-column>
      <el-table-column v-for="(item, index) in tableColumn" :key="index" v-bind="item" :prop="item.prop"
        :label="item.label" :formatter="item.formatter" :fixed="item.fixed" :min-width="item.minWidth" resizable
        show-overflow-tooltip :align="item.align" :sortable="item.sortable" :sort-method="sortMethod"
        :sort-by="item.prop" :header-align="item.headerAlign" :width="item.width">
        <template #default="scope">
          <slot :name="item.slotName" :row="scope.row">
            {{ item.isFixed && item.isFixed > 0 ? toFixedFun(scope.row[item.prop], item.isFixed) : scope.row[item.prop]
            }}
          </slot>
        </template>
      </el-table-column>
      <el-table-column :label="operationTable.label" align="center" :width="operationTable.width"
        v-if="operationTable && operationTable.buttons">
        <template #default="scope">
          <!-- 默认操作按钮，可根据需求修改 -->
          <el-button v-for="(item, index) in operationTable.buttons" :key="index" size="small" text :type="item.type"
            @click="item.clickFunction && item.clickFunction(scope.row)">{{item.label}}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, defineProps, defineEmits, computed } from 'vue';

// 定义 props 类型
interface Props {
  height?: number;
  selection?: boolean;
  selectionList?: any[];
  emptyText?: string;
  tableData?: any[];
  tableColumn?: Array<{
    prop: string;
    label: string;
    formatter?: Function;
    fixed?: string | boolean;
    minWidth?: string | number;
    align?: string;
    sortable?: boolean | string;
    headerAlign?: string;
    width?: string | number;
    slotName?: string;
    isFixed?: number;
  }>;
  spanMethod?: Function;
  operationTable?: {
    label: string;
    type: string;
    icon: string;
    width: string | number;
    buttons?: Array<{
      label: string;
      type: string;
      icon: string;
      clickFunction?: Function;
    }>;
  };
}

// 定义 props
const props = defineProps<Props>();

// 定义 emits
const emit = defineEmits(['select', 'selectAll', 'sortChange', 'rowClick','‘edit’','delete']);

// 定义响应式数据
const isLoading = ref(false);
const tableRef = ref<HTMLElement | null>(null);

// 监听 selectionList 变化
watch(() => props.selectionList, (val) => {
  if (tableRef.value) {
    val?.map((item) => {
      (tableRef.value as any).toggleRowSelection(item, true);
    });
  }
});

// 方法定义
const selectionSelect = (val: any, row: any) => {
  emit('select', val, row);
};

const selectionSelectAll = (val: any) => {
  emit('selectAll', val);
};

const sortMethod = (val: any) => { };

const sortChange = (column: any, prop: any, order: any) => {
  emit('sortChange', column, prop, order);
};

const rowClick = (val: any) => {
  emit('rowClick', val);
};

const toFixedFun = (val: any, num: number) => {
  const NumVal = Number(val);
  const regPos = /^\d+(\.\d+)?$/; // 非负浮点数
  const regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; // 负浮点数
  if (regPos.test(val) || regNeg.test(val)) {
    return NumVal.toFixed(num);
  } else {
    return val;
  }
};
</script>