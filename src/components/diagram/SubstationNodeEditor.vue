<template>
	<div
		class="absolute top-5 right-5 z-10 border border-gray-300 shadow-lg bg-white transition-all duration-300 position-editor-panel flex"
		:class="[collapsed ? 'collapsed-panel' : 'expanded-panel']"
		style="max-height: calc(100% - 180px)"
	>
		<!-- 面板内容 -->
		<div v-if="!collapsed" class="panel-content p-4 flex-grow overflow-y-auto">
			<h3 class="text-lg font-bold text-gray-800 mb-4 editor-header">节点位置编辑工具</h3>

			<!-- 选中节点编辑区域 -->
			<div v-if="selectedNodeData" class="mb-4 border-b pb-4 node-info-section">
				<h4 class="font-medium text-gray-700 mb-2">当前选中节点</h4>
				<div class="mb-2">
					<span class="text-gray-600">名称:</span>
					<span class="ml-2 font-medium">{{ selectedNodeData.name }}</span>
				</div>
				<div class="mb-2">
					<span class="text-gray-600">类型:</span>
					<span class="ml-2">{{ selectedNodeData.type === 'plant' ? '发电厂' : '变电站' }}</span>
				</div>

				<!-- 坐标输入区域 -->
				<div class="grid grid-cols-2 gap-4 mb-2">
					<div>
						<span class="text-gray-600">X 坐标:</span>
						<el-input-number v-model="localNodeData.x" :precision="1" :step="10" size="small" class="!w-full mt-1" @change="updateNodePosition" />
					</div>
					<div>
						<span class="text-gray-600">Y 坐标:</span>
						<el-input-number v-model="localNodeData.y" :precision="1" :step="10" size="small" class="!w-full mt-1" @change="updateNodePosition" />
					</div>
				</div>
			</div>

			<!-- 没有选中节点时的提示 -->
			<div v-else class="mb-4 border-b pb-4 text-center text-gray-500 py-4">点击图表中的节点以编辑其位置</div>

			<!-- 数据操作区域 -->
			<div class="mb-4">
				<h4 class="font-medium text-gray-700 mb-2">数据操作</h4>

				<!-- 导入导出按钮 -->
				<div class="flex flex-wrap gap-2 mb-4">
					<el-tooltip content="导出当前所有节点位置数据到JSON" placement="top">
						<el-button type="primary" size="small" @click="exportNodesData">
							<el-icon class="mr-1"><Download /></el-icon>导出数据
						</el-button>
					</el-tooltip>
					<el-tooltip content="将编辑后的JSON应用到图表" placement="top">
						<el-button type="success" size="small" @click="applyNodesData">
							<el-icon class="mr-1"><Check /></el-icon>应用数据
						</el-button>
					</el-tooltip>
					<el-tooltip content="重新计算所有节点位置" placement="top">
						<el-button type="warning" size="small" @click="resetLayout">
							<el-icon class="mr-1"><Refresh /></el-icon>重新布局
						</el-button>
					</el-tooltip>
				</div>

				<!-- JSON编辑器 -->
				<el-input v-model="nodesDataJson" type="textarea" placeholder="节点数据 JSON" :rows="8" resize="none" class="font-mono text-xs" />

				<div class="text-xs text-gray-500 mt-1">提示: 修改JSON后点击"应用数据"按钮生效</div>

				<!-- 额外功能按钮 -->
				<div class="flex justify-between mt-4">
					<el-tooltip content="复制JSON到剪贴板" placement="top">
						<el-button size="small" @click="copyToClipboard">
							<el-icon><DocumentCopy /></el-icon>
						</el-button>
					</el-tooltip>
					<el-tooltip content="格式化JSON" placement="top">
						<el-button size="small" @click="formatJSON">
							<el-icon><SortUp /></el-icon>
						</el-button>
					</el-tooltip>
					<el-tooltip content="清空JSON编辑器" placement="top">
						<el-button size="small" @click="nodesDataJson = ''">
							<el-icon><Delete /></el-icon>
						</el-button>
					</el-tooltip>
				</div>
			</div>
		</div>
		<div class="border-l flex items-center px-1" @click="collapsed = !collapsed">
			<div class="toggle-button cursor-pointer p-2 rounded hover:bg-gray-100 flex items-center justify-center">
				<el-icon :size="20" class="text-gray-600 hover:text-gray-900">
					<ArrowRight v-if="!collapsed" />
					<ArrowLeft v-else />
				</el-icon>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import * as go from 'gojs';
import { ArrowLeft, ArrowRight, Download, Check, Refresh, DocumentCopy, SortUp, Delete } from '@element-plus/icons-vue';

// 统一props定义结构
const props = defineProps<{
	diagram: go.Diagram | null;
	selectedNodeData: SubstationNode | null;
	collapsed?: boolean;
}>();

// 内部状态管理
const collapsed = ref(props.collapsed || false);
const nodesDataJson = ref('');

// 创建本地节点数据副本，避免直接修改props
const localNodeData = ref<SubstationNode>({
	key: '',
	name: '',
	type: '',
	category: 'station',
	x: 0,
	y: 0,
	color: '#1c57ea',
});

// 监听选中节点变化并同步到本地数据
watch(
	() => props.selectedNodeData,
	(newValue) => {
		if (newValue) {
			localNodeData.value = { ...newValue };

			// 如果节点数据中没有坐标，尝试从diagram中获取实际位置
			if (localNodeData.value.x === undefined || localNodeData.value.y === undefined) {
				const myDiagram = diagram.value;
				if (myDiagram && newValue.key) {
					const node = myDiagram.findNodeForKey(newValue.key);
					if (node) {
						const location = node.location;
						localNodeData.value.x = parseFloat(location.x.toFixed(1));
						localNodeData.value.y = parseFloat(location.y.toFixed(1));
					}
				}
			}
		}
	},
	{ deep: true, immediate: true }
);

// 统一diagram获取方式
const diagram = computed(() => props.diagram);
const selectedNodeData = computed(() => props.selectedNodeData);

// ===== 工具函数 =====

/**
 * 统一的错误处理和事务管理
 */
const executeWithTransaction = (transactionName: string, operation: () => void, successMessage?: string) => {
	const myDiagram = diagram.value;
	if (!myDiagram) {
		ElMessage.warning('图表不存在');
		return false;
	}

	myDiagram.startTransaction(transactionName);
	try {
		operation();
		myDiagram.commitTransaction(transactionName);
		if (successMessage) {
			ElMessage.success(successMessage);
		}
		return true;
	} catch (error) {
		myDiagram.rollbackTransaction();
		console.error(`${transactionName}失败:`, error);
		ElMessage.error(`${transactionName}失败`);
		return false;
	}
};

/**
 * 统一的JSON处理工具
 */
const parseJsonSafely = (jsonStr: string) => {
	try {
		return { success: true, data: JSON.parse(jsonStr) };
	} catch (error) {
		return { success: false, error: error instanceof Error ? error.message : '未知错误' };
	}
};

// ===== 核心编辑功能方法 =====

/**
 * 更新节点位置
 */
const updateNodePosition = () => {
	if (!selectedNodeData.value || !localNodeData.value) {
		ElMessage.warning('没有选中的节点');
		return;
	}

	executeWithTransaction(
		'updatePosition',
		() => {
			const myDiagram = diagram.value!;
			const node = myDiagram.findNodeForKey(localNodeData.value.key);
			if (!node) {
				throw new Error('未找到指定节点');
			}

			// 应用新位置
			const newPos = new go.Point(localNodeData.value.x || 0, localNodeData.value.y || 0);
			node.location = newPos;

			// 更新数据模型中的位置信息
			myDiagram.model.setDataProperty(node.data, 'x', localNodeData.value.x);
			myDiagram.model.setDataProperty(node.data, 'y', localNodeData.value.y);
		},
		'节点位置已更新'
	);
};

/**
 * 导出所有节点的位置数据为JSON
 */
const exportNodesData = () => {
	const myDiagram = diagram.value;
	if (!myDiagram) {
		ElMessage.warning('图表不存在');
		return;
	}

	try {
		// 获取图表中所有节点数据
		const nodeArray = myDiagram.model.nodeDataArray as SubstationNode[];
		const nodeDataWithPos = nodeArray.map((node) => {
			// 查找节点对象以获取当前位置
			const nodeObj = myDiagram.findNodeForKey(node.key);
			let position = { x: 0, y: 0 };

			if (nodeObj) {
				const loc = nodeObj.location;
				position = { x: parseFloat(loc.x.toFixed(1)), y: parseFloat(loc.y.toFixed(1)) };
			}

			// 返回节点数据，包含位置信息
			return {
				key: node.key,
				name: node.name,
				type: node.type,
				category: node.category,
				x: position.x,
				y: position.y,
			};
		});

		// 更新文本区域内容
		nodesDataJson.value = JSON.stringify(nodeDataWithPos, null, 2);
		ElMessage.success('节点位置数据已导出');
	} catch (error) {
		console.error('导出节点数据失败:', error);
		ElMessage.error('导出节点数据失败');
	}
};

/**
 * 应用JSON编辑器中的节点数据
 */
const applyNodesData = () => {
	if (!nodesDataJson.value?.trim()) {
		ElMessage.warning('JSON数据不能为空');
		return;
	}

	const parseResult = parseJsonSafely(nodesDataJson.value);
	if (!parseResult.success) {
		ElMessage.error(`JSON格式错误: ${parseResult.error}`);
		return;
	}

	const nodeDataArray = parseResult.data;
	if (!Array.isArray(nodeDataArray)) {
		ElMessage.error('JSON数据格式错误：应为数组格式');
		return;
	}

	executeWithTransaction(
		'applyPositions',
		() => {
			const myDiagram = diagram.value!;

			// 应用位置到每个节点
			nodeDataArray.forEach((data: any) => {
				if (data.key && data.x !== undefined && data.y !== undefined) {
					const node = myDiagram.findNodeForKey(data.key);
					if (node) {
						node.location = new go.Point(data.x, data.y);

						// 更新数据模型
						myDiagram.model.setDataProperty(node.data, 'x', data.x);
						myDiagram.model.setDataProperty(node.data, 'y', data.y);

						// 更新其他属性
						if (data.name) myDiagram.model.setDataProperty(node.data, 'name', data.name);
					}
				}
			});
		},
		'节点位置数据已应用'
	);
};

/**
 * 重新应用力导向布局
 */
const resetLayout = () => {
	executeWithTransaction(
		'resetLayout',
		() => {
			const myDiagram = diagram.value!;

			// 确保布局是力导向布局
			if (myDiagram.layout instanceof go.ForceDirectedLayout) {
				const layout = myDiagram.layout as go.ForceDirectedLayout;

				// 重新启用布局算法
				layout.isInitial = true;
				layout.isOngoing = true;

				// 触发布局
				myDiagram.layoutDiagram(true);

				setTimeout(() => {
					// 布局完成后，停止自动布局以便手动调整
					layout.isInitial = false;
					layout.isOngoing = false;

					// 导出新的位置数据
					exportNodesData();
				}, 2000); // 给布局算法2秒时间完成
			} else {
				// 如果不是力导向布局，设置一个新的
				const layout = new go.ForceDirectedLayout();
				layout.maxIterations = 200;
				layout.arrangementSpacing = new go.Size(80, 80);
				layout.isInitial = true;
				layout.isOngoing = false;

				myDiagram.layout = layout;
				myDiagram.layoutDiagram(true);
			}
		},
		'正在重新计算节点位置...'
	);
};

/**
 * 复制JSON数据到剪贴板
 */
const copyToClipboard = async () => {
	if (!nodesDataJson.value) {
		ElMessage.warning('没有数据可复制');
		return;
	}

	try {
		await navigator.clipboard.writeText(nodesDataJson.value);
		ElMessage.success('已复制到剪贴板');
	} catch (error) {
		console.error('复制失败:', error);
		ElMessage.error('复制失败');
	}
};

/**
 * 格式化JSON数据
 */
const formatJSON = () => {
	if (!nodesDataJson.value) {
		ElMessage.warning('没有数据可格式化');
		return;
	}

	const parseResult = parseJsonSafely(nodesDataJson.value);
	if (!parseResult.success) {
		ElMessage.error('JSON格式错误，无法格式化');
		return;
	}

	nodesDataJson.value = JSON.stringify(parseResult.data, null, 2);
	ElMessage.success('JSON已格式化');
};

// 暴露组件接口
defineExpose({
	selectedNodeData: localNodeData,
	collapsed,
	exportNodesData,
	applyNodesData,
	resetLayout,
	updateSelectedNode: (node: SubstationNode | null) => {
		if (node) {
			localNodeData.value = { ...node };
		}
	},
});
</script>

<style scoped>
.position-editor-panel {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	transition: all 0.3s ease;
}

.position-editor-panel:hover {
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 收起状态的面板 */
.collapsed-panel {
	width: auto;
	min-width: 40px;
	overflow: hidden;
}

/* 展开状态的面板 */
.expanded-panel {
	width: 400px;
}

/* 折叠/展开按钮样式 */
.toggle-button {
	transition: all 0.2s ease;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.toggle-button:hover {
	background-color: #f2f6fc;
}

.editor-header {
	border-bottom: 1px solid #ebeef5;
	padding-bottom: 8px;
	margin-bottom: 16px;
}

.node-info-section {
	background-color: #f9f9f9;
	border-radius: 4px;
	padding: 8px;
	margin-bottom: 12px;
}

/* 自定义滚动条 */
:deep(.el-textarea__inner) {
	scrollbar-width: thin;
	scrollbar-color: #d4d4d4 #f5f5f5;
}

:deep(.el-textarea__inner::-webkit-scrollbar) {
	width: 8px;
	height: 8px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-track) {
	background: #f5f5f5;
	border-radius: 4px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
	background: #d4d4d4;
	border-radius: 4px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-thumb:hover) {
	background: #c1c1c1;
}
</style>
