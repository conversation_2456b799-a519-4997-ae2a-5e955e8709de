<template>
	<div
		class="absolute top-5 right-5 z-10 border border-gray-300 shadow-lg bg-white transition-all duration-300 position-editor-panel flex"
		:class="[collapsed ? 'collapsed-panel' : 'expanded-panel']"
		style="max-height: calc(100% - 180px)"
	>
		<!-- 面板内容 -->
		<div v-if="!collapsed" class="panel-content p-4 flex-grow overflow-y-auto">
			<h3 class="text-lg font-bold text-gray-800 mb-4 editor-header">节点位置编辑工具</h3>

			<!-- 选中节点编辑区域 -->
			<div v-if="selectedNodeData && selectedNodeData.name" class="mb-4 border-b pb-4 node-info-section">
				<h4 class="font-medium text-gray-700 mb-2">当前选中节点</h4>
				<div class="mb-2">
					<span class="text-gray-600">名称:</span>
					<span class="ml-2 font-medium">{{ selectedNodeData.name }}</span>
				</div>
				<div class="mb-2">
					<span class="text-gray-600">类型:</span>
					<span class="ml-2">{{ selectedNodeData.type }}</span>
				</div>
				<div class="mb-2">
					<span class="text-gray-600">角度:</span>
					<span class="ml-2">{{ localNodeData.angle }}°</span>
				</div>

				<!-- 坐标输入区域 -->
				<div class="grid grid-cols-2 gap-4 mb-2">
					<div>
						<span class="text-gray-600">X 坐标:</span>
						<el-input-number v-model="localNodeData.posX" :precision="1" :step="10" size="small" class="!w-full mt-1" @change="updateNodePosition" />
					</div>
					<div>
						<span class="text-gray-600">Y 坐标:</span>
						<el-input-number v-model="localNodeData.posY" :precision="1" :step="10" size="small" class="!w-full mt-1" @change="updateNodePosition" />
					</div>
				</div>

				<!-- 角度输入区域 -->
				<div class="mb-3">
					<span class="text-gray-600">角度 (度):</span>
					<div class="mt-1">
						<el-input-number
							v-model="localNodeData.angle"
							:min="0"
							:max="360"
							:precision="0"
							:step="15"
							size="small"
							class="!w-full"
							@change="updateNodeAngle"
						/>
					</div>
					<!-- 角度预设快捷按钮 -->
					<div class="flex gap-1 mt-2">
						<el-button
							v-for="presetAngle in [0, 90, 180, 270]"
							:key="presetAngle"
							size="small"
							type="default"
							@click="setPresetAngle(presetAngle)"
							class="text-xs px-2"
						>
							{{ presetAngle }}°
						</el-button>
					</div>
				</div>

				<!-- 母线宽度输入区域，仅对母线节点显示 -->
				<div v-if="selectedNodeData.type === 'BusbarSection'" class="mb-2">
					<span class="text-gray-600">母线宽度:</span>
					<el-input-number v-model="localNodeData.width" :min="50" :max="500" :step="10" size="small" class="!w-full mt-1" @change="updateBusWidth" />
				</div>
			</div>

			<!-- 没有选中节点时的提示 -->
			<div v-else class="mb-4 border-b pb-4 text-center text-gray-500 py-4">点击图表中的节点以编辑其位置</div>

			<!-- 数据操作区域 -->
			<div class="mb-4">
				<h4 class="font-medium text-gray-700 mb-2">数据操作</h4>

				<!-- 导入导出按钮 -->
				<div class="flex flex-wrap gap-2 mb-4">
					<el-tooltip content="导出当前所有节点位置数据到JSON" placement="top">
						<el-button type="primary" size="small" @click="exportNodesData">
							<el-icon class="mr-1"><Download /></el-icon>导出数据
						</el-button>
					</el-tooltip>
					<el-tooltip content="将编辑后的JSON应用到图表" placement="top">
						<el-button type="success" size="small" @click="applyNodesData">
							<el-icon class="mr-1"><Check /></el-icon>应用数据
						</el-button>
					</el-tooltip>
					<el-tooltip content="重新计算所有节点位置" placement="top">
						<el-button type="warning" size="small" @click="resetLayout">
							<el-icon class="mr-1"><Refresh /></el-icon>重新布局
						</el-button>
					</el-tooltip>
				</div>

				<!-- JSON编辑器 -->
				<el-input v-model="nodesDataJson" type="textarea" placeholder="节点数据 JSON" :rows="8" resize="none" class="font-mono text-xs" />

				<div class="text-xs text-gray-500 mt-1">提示: 修改JSON后点击"应用数据"按钮生效</div>

				<!-- 额外功能按钮 -->
				<div class="flex justify-between mt-4">
					<el-tooltip content="复制JSON到剪贴板" placement="top">
						<el-button size="small" @click="copyToClipboard">
							<el-icon><DocumentCopy /></el-icon>
						</el-button>
					</el-tooltip>
					<el-tooltip content="格式化JSON" placement="top">
						<el-button size="small" @click="formatJSON">
							<el-icon><SortUp /></el-icon>
						</el-button>
					</el-tooltip>
					<el-tooltip content="清空JSON编辑器" placement="top">
						<el-button size="small" @click="nodesDataJson = ''">
							<el-icon><Delete /></el-icon>
						</el-button>
					</el-tooltip>
				</div>
			</div>
		</div>
		<div class="border-l flex items-center px-1" @click="collapsed = !collapsed">
			<div class="toggle-button cursor-pointer p-2 rounded hover:bg-gray-100 flex items-center justify-center">
				<el-icon :size="20" class="text-gray-600 hover:text-gray-900">
					<ArrowRight v-if="!collapsed" />
					<ArrowLeft v-else />
				</el-icon>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { ArrowLeft, ArrowRight, Download, Check, Refresh, DocumentCopy, SortUp, Delete } from '@element-plus/icons-vue';

// 统一props定义结构
const props = defineProps<{
	diagram: go.Diagram | null;
	selectedNodeData: SingleLineNode | null;
	collapsed?: boolean;
}>();

// 内部状态管理
const collapsed = ref(props.collapsed || false);
const nodesDataJson = ref<string>('');

// 创建本地节点数据副本，避免直接修改props
const localNodeData = ref<SingleLineNode>({
	key: '',
	name: '',
	type: '',
	category: '',
	color: '#1c57ea',
	pos: [0, 0],
	angle: 0,
	posX: 0,
	posY: 0,
	width: 150,
});

// 监听选中节点变化并同步到本地数据
watch(
	() => props.selectedNodeData,
	(newValue) => {
		if (newValue) {
			localNodeData.value = { ...newValue };

			// 如果节点数据中没有坐标，尝试从diagram中获取实际位置
			if (localNodeData.value.posX === undefined || localNodeData.value.posY === undefined) {
				const myDiagram = diagram.value;
				if (myDiagram && newValue.key) {
					const node = myDiagram.findNodeForKey(newValue.key);
					if (node) {
						const location = node.location;
						localNodeData.value.posX = parseFloat(location.x.toFixed(1));
						localNodeData.value.posY = parseFloat(location.y.toFixed(1));
					}
				}
			}
		}
	},
	{ deep: true, immediate: true }
);

// 统一diagram获取方式
const diagram = computed(() => props.diagram);
const selectedNodeData = computed(() => props.selectedNodeData);

// ===== 工具函数 =====

/**
 * 统一的错误处理和事务管理
 */
const executeWithTransaction = (transactionName: string, operation: () => void, successMessage?: string) => {
	const myDiagram = diagram.value;
	if (!myDiagram) {
		ElMessage.warning('图表不存在');
		return false;
	}

	myDiagram.startTransaction(transactionName);
	try {
		operation();
		myDiagram.commitTransaction(transactionName);
		if (successMessage) {
			ElMessage.success(successMessage);
		}
		return true;
	} catch (error) {
		myDiagram.rollbackTransaction();
		console.error(`${transactionName}失败:`, error);
		ElMessage.error(`${transactionName}失败`);
		return false;
	}
};

/**
 * 统一的JSON处理工具
 */
const parseJsonSafely = (jsonStr: string) => {
	try {
		return { success: true, data: JSON.parse(jsonStr) };
	} catch (error) {
		return { success: false, error: error instanceof Error ? error.message : '未知错误' };
	}
};

// ===== 核心编辑功能方法 =====

/**
 * 更新节点位置
 */
const updateNodePosition = () => {
	if (!selectedNodeData.value || !localNodeData.value) {
		ElMessage.warning('没有选中的节点');
		return;
	}

	executeWithTransaction(
		'updatePosition',
		() => {
			const myDiagram = diagram.value!;
			const node = myDiagram.findNodeForKey(localNodeData.value.key);
			if (!node) {
				throw new Error('未找到指定节点');
			}

			// 更新节点位置
			const newLocation = new go.Point(localNodeData.value.posX, localNodeData.value.posY);
			myDiagram.model.setDataProperty(node.data, 'pos', [localNodeData.value.posX, localNodeData.value.posY]);
			node.location = newLocation;

			// 更新相关连线路径
			node.findLinksConnected().each((link) => {
				if (link instanceof go.Link) {
					link.updateRoute();
				}
			});
		},
		'节点位置已更新'
	);
};

/**
 * 更新节点角度
 */
const updateNodeAngle = () => {
	if (!selectedNodeData.value || !localNodeData.value) {
		ElMessage.warning('没有选中的节点');
		return;
	}

	executeWithTransaction(
		'updateAngle',
		() => {
			const myDiagram = diagram.value!;
			const node = myDiagram.findNodeForKey(localNodeData.value.key);
			if (!node) {
				throw new Error('未找到指定节点');
			}

			// 确保角度值在0-360范围内
			const normalizedAngle = ((localNodeData.value.angle % 360) + 360) % 360;
			localNodeData.value.angle = normalizedAngle;

			// 更新节点角度
			myDiagram.model.setDataProperty(node.data, 'angle', normalizedAngle);
			node.angle = normalizedAngle;
			node.updateTargetBindings();
		},
		`节点角度已更新为 ${localNodeData.value.angle}°`
	);
};

/**
 * 更新母线宽度
 */
const updateBusWidth = () => {
	if (!selectedNodeData.value || !localNodeData.value) {
		ElMessage.warning('没有选中的节点');
		return;
	}

	if (localNodeData.value.type !== 'BusbarSection') {
		ElMessage.warning('只有母线节点支持宽度调整');
		return;
	}

	executeWithTransaction(
		'updateBusWidth',
		() => {
			const myDiagram = diagram.value!;
			const node = myDiagram.findNodeForKey(localNodeData.value.key);
			if (!node) {
				throw new Error('未找到指定母线节点');
			}

			// 更新母线宽度
			myDiagram.model.setDataProperty(node.data, 'width', localNodeData.value.width);
			node.updateTargetBindings();
		},
		'母线宽度已更新'
	);
};

/**
 * 导出节点数据为JSON
 */
const exportNodesData = () => {
	const myDiagram = diagram.value;
	if (!myDiagram) {
		ElMessage.warning('图表不存在');
		return;
	}

	try {
		const nodesData: any[] = [];

		myDiagram.nodes.each((node: go.Node) => {
			// 跳过Comment节点
			if (node.data?.category === 'Comment') {
				return;
			}

			const nodeInfo = {
				key: node.data.key,
				name: node.data.name,
				type: node.data.type,
				category: node.data.category,
				posX: parseFloat(node.location.x.toFixed(1)),
				posY: parseFloat(node.location.y.toFixed(1)),
				angle: node.data.angle || 0,
				color: node.data.color,
				width: node.data.width,
				voltage: node.data.voltage,
			};
			nodesData.push(nodeInfo);
		});

		nodesDataJson.value = JSON.stringify(nodesData, null, 2);
		ElMessage.success('节点数据已导出');
	} catch (error) {
		console.error('导出节点数据失败:', error);
		ElMessage.error('导出节点数据失败');
	}
};

/**
 * 应用JSON编辑器中的节点数据
 */
const applyNodesData = () => {
	if (!nodesDataJson.value?.trim()) {
		ElMessage.warning('JSON数据不能为空');
		return;
	}

	const parseResult = parseJsonSafely(nodesDataJson.value);
	if (!parseResult.success) {
		ElMessage.error(`JSON格式错误: ${parseResult.error}`);
		return;
	}

	const nodesData = parseResult.data;
	if (!Array.isArray(nodesData)) {
		ElMessage.error('JSON数据格式错误：应为数组格式');
		return;
	}

	executeWithTransaction(
		'applyNodesData',
		() => {
			const myDiagram = diagram.value!;

			nodesData.forEach((nodeInfo: any) => {
				const node = myDiagram.findNodeForKey(nodeInfo.key);
				if (!node) return;

				// 更新节点位置
				if (typeof nodeInfo.posX === 'number' && typeof nodeInfo.posY === 'number') {
					const newLocation = new go.Point(nodeInfo.posX, nodeInfo.posY);
					myDiagram.model.setDataProperty(node.data, 'pos', [nodeInfo.posX, nodeInfo.posY]);
					node.location = newLocation;
				}

				// 更新其他属性
				if (nodeInfo.name) myDiagram.model.setDataProperty(node.data, 'name', nodeInfo.name);
				if (nodeInfo.color) myDiagram.model.setDataProperty(node.data, 'color', nodeInfo.color);
				if (nodeInfo.width && nodeInfo.type === 'BusbarSection') myDiagram.model.setDataProperty(node.data, 'width', nodeInfo.width);

				// 更新角度
				if (typeof nodeInfo.angle === 'number') {
					const normalizedAngle = ((nodeInfo.angle % 360) + 360) % 360;
					myDiagram.model.setDataProperty(node.data, 'angle', normalizedAngle);
					node.angle = normalizedAngle;
				}

				node.updateTargetBindings();
			});

			// 更新所有连线路径
			myDiagram.links.each((link) => {
				if (link instanceof go.Link) {
					link.updateRoute();
				}
			});
		},
		'节点数据已应用'
	);
};

/**
 * 重新应用力导向布局
 */
const resetLayout = () => {
	executeWithTransaction(
		'resetLayout',
		() => {
			const myDiagram = diagram.value!;

			// 设置力导向布局
			const layout = new go.ForceDirectedLayout();
			layout.maxIterations = 200;
			layout.arrangementSpacing = new go.Size(80, 80);
			layout.isInitial = true;
			layout.isOngoing = false;

			myDiagram.layout = layout;
			myDiagram.layoutDiagram(true);
		},
		'布局已应用'
	);
};

/**
 * 复制JSON数据到剪贴板
 */
const copyToClipboard = async () => {
	if (!nodesDataJson.value) {
		ElMessage.warning('没有数据可复制');
		return;
	}

	try {
		await navigator.clipboard.writeText(nodesDataJson.value);
		ElMessage.success('已复制到剪贴板');
	} catch (error) {
		console.error('复制失败:', error);
		ElMessage.error('复制失败');
	}
};

/**
 * 格式化JSON数据
 */
const formatJSON = () => {
	if (!nodesDataJson.value) {
		ElMessage.warning('没有数据可格式化');
		return;
	}

	const parseResult = parseJsonSafely(nodesDataJson.value);
	if (!parseResult.success) {
		ElMessage.error('JSON格式错误，无法格式化');
		return;
	}

	nodesDataJson.value = JSON.stringify(parseResult.data, null, 2);
	ElMessage.success('JSON已格式化');
};

/**
 * 设置预设角度
 */
const setPresetAngle = (angle: number) => {
	if (localNodeData.value && selectedNodeData.value) {
		localNodeData.value.angle = angle;
		updateNodeAngle();
	}
};

// 暴露组件接口
defineExpose({
	selectedNodeData: localNodeData,
	collapsed,
	exportNodesData,
	applyNodesData,
	resetLayout,
	updateSelectedNode: (node: SingleLineNode | null) => {
		if (node) {
			localNodeData.value = { ...node };
		}
	},
});
</script>

<style scoped>
/* 节点编辑器面板样式 */
.position-editor-panel {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	transition: all 0.3s ease;
}

.position-editor-panel:hover {
	box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 收起状态的面板 */
.collapsed-panel {
	width: auto;
	min-width: 40px;
	overflow: hidden;
}

/* 展开状态的面板 */
.expanded-panel {
	width: 400px;
}

/* 折叠/展开按钮样式 */
.toggle-button {
	transition: all 0.2s ease;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.toggle-button:hover {
	background-color: #f2f6fc;
}

.editor-header {
	border-bottom: 1px solid #ebeef5;
	padding-bottom: 8px;
	margin-bottom: 16px;
}

.node-info-section {
	background-color: #f9f9f9;
	border-radius: 4px;
	padding: 8px;
	margin-bottom: 12px;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
	width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
	background: #999;
}
</style>
