<template>
	<div class="h-full w-full">
		<SubstationGraph ref="substationGraphRef" @node-selected="handleNodeSelected" />
		<SubstationNodeEditor :diagram="diagram" :selected-node-data="selectedNodeData" />
	</div>
</template>

<script setup lang="ts">
import { provide, computed, ref } from 'vue';
import SubstationGraph from '/@/views/power-flow-calculation/graph/substation/index.vue';
import SubstationNodeEditor from '/@/components/diagram/SubstationNodeEditor.vue';
const dataPacket = computed(() => ({ id: '2', isHideNodeInfo: true }));
provide('dataPacket', dataPacket);
const selectedNodeData = ref<any>(null);
const substationGraphRef = ref<InstanceType<typeof SubstationGraph> | null>(null);
const diagram = computed(() => {
	return substationGraphRef.value?.diagram || null;
});

const handleNodeSelected = (node: any) => {
	selectedNodeData.value = {
		...node,
	};
};
</script>

<style lang="scss" scoped></style>
