<template>
	<div class="w-full h-full relative rounded-md">
		<div ref="diagram" class="w-full h-full bg-white"></div>
		<div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
			<div class="flex flex-col items-center">
				<el-icon class="animate-spin text-blue-600 text-2xl mb-2">
					<Loading />
				</el-icon>
				<span class="text-gray-600">加载厂站图中...</span>
			</div>
		</div>
		<div class="absolute top-5 left-5 z-10 flex space-x-2">
			<!-- <el-button @click="saveDiagram">保存</el-button> -->
			<!-- <el-button @click="loadDiagram">加载</el-button> -->
			<el-select v-model="selectedNode" filterable remote reserve-keyword placeholder="输入关键词搜索节点"
				:remote-method="searchNodes" clearable class="!w-80" @change="handleNodeSelect">
				<el-option v-for="item in nodeOptions" :key="item.key" :label="item.label" :value="item.key">
					<span>{{ item.label }}</span>
					<span class="text-xs text-gray-500 ml-2">({{ item.type === 'plant' ? '发电厂' : '变电站' }})</span>
				</el-option>
			</el-select>
		</div>
		<div ref="overviewRef" class="absolute bottom-5 right-5 z-10 border border-gray-300 shadow-lg bg-white"
			style="width: 200px; height: 150px"></div>
		<!--线路右键功能 新增开始-->
		<ul v-show="gridLineVisible && props.lineOperation" :style="menuStyle" class="contextmenu" ref="menuRef">
			<li @click="updateService">{{ lineRightName }}</li>
		</ul>
		<!--线路右键功能 新增结束-->
		<el-dialog destroy-on-close class="!p-0" fullscreen body-class="!h-full !max-h-full" header-class="!p-0"
			v-model="isShowNodeInfo" width="90%" height="90%">
			<SingleLineGraph :node-info="nodeInfo" />
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, inject, ComputedRef, onBeforeUnmount } from 'vue';
import * as go from 'gojs';
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
import { ElMessage,ElMessageBox  } from 'element-plus';
import { ParallelRouteLink } from './ParallelRouteLink';
import { query_substation_graph } from './api';
import SingleLineGraph from '../single-line/index.vue';
// 新增开始
import * as calculateApi from '../../../FaultSet/Popup/Add/calculateApi';
// 定义 emits
const emit = defineEmits<{
	(e: 'lineChange', currentLink: {},selection: any[],): void;
}>();
// 定义父组件传过来的值
const props = defineProps({
	lineOperation: {
		type: Boolean,
		default: false,
	}
});
// 新增结束
// 导入电压等级配置
import { COLORS, ZOOM, getVoltageConfig, getVoltageColor, getVoltageLineWidth, MAX_PARALLEL_LINES, getLineSegmentFraction } from './config';

// 引用图表容器元素
const diagram = ref<HTMLDivElement | null>(null);
// 缩略图容器元素引用
const overviewRef = ref<HTMLDivElement | null>(null);
// 图表实例
let myDiagram: go.Diagram | null = null;
// 缩略图实例
let myOverview: go.Overview | null = null;
// GraphObject工厂函数
let $: any = null;
// 新增开始
// 线路右击功能
const gridLineVisible = ref(false);
const menuStyle = ref({ left: '0px', top: '0px' });
const lineRightName = ref('停运')
let currentLink: go.Link | null = null;
const menuRef = ref<HTMLDivElement>();
// 新增结束
// 搜索选择相关
const selectedNode = ref('');
const nodeOptions = ref<{ key: string; label: string; type: string }[]>([]);
const allNodes = ref<SubstationNode[]>([]);

/**
 * 获取汉字的拼音首字母
 * 简化版拼音匹配，仅支持常用汉字
 * @param str 输入字符串
 * @returns 拼音首字母字符串
 */
const getPinyinInitials = (str: string): string => {
	if (!str) return '';

	// 常用汉字拼音首字母映射表（简化版）
	const pinyinMap: { [key: string]: string } = {
		变: 'b',
		电: 'd',
		站: 'z',
		厂: 'c',
		能: 'n',
		源: 'y',
		火: 'h',
		风: 'f',
		太: 't',
		阳: 'y',
		水: 's',
		核: 'h',
		发: 'f',
		城: 'c',
		区: 'q',
		南: 'n',
		北: 'b',
		东: 'd',
		西: 'x',
		中: 'z',
		新: 'x',
		河: 'h',
		湖: 'h',
		山: 's',
		海: 'h',
		洋: 'y',
		京: 'j',
		津: 'j',
		冀: 'j',
		晋: 'j',
		蒙: 'm',
		辽: 'l',
		吉: 'j',
		黑: 'h',
		沪: 'h',
		苏: 's',
		浙: 'z',
		皖: 'w',
		闽: 'm',
		赣: 'g',
		鲁: 'l',
		豫: 'y',
		鄂: 'e',
		湘: 'x',
		粤: 'y',
		桂: 'g',
		琼: 'q',
		渝: 'y',
		川: 'c',
		贵: 'g',
		云: 'y',
		藏: 'z',
		陕: 's',
		甘: 'g',
		青: 'q',
		宁: 'n',
		新疆: 'xj',
	};

	let result = '';
	for (let i = 0; i < str.length; i++) {
		const char = str[i];
		// 如果是汉字，查找映射表
		if (/[\u4e00-\u9fa5]/.test(char)) {
			if (pinyinMap[char]) {
				result += pinyinMap[char];
			}
		} else {
			// 非汉字直接添加
			result += char.toLowerCase();
		}
	}
	return result;
};

/**
 * 匹配节点是否符合搜索条件
 * 匹配名称、类型、区域等多个属性
 * 支持拼音首字母匹配
 * @param nodeData 节点数据
 * @param searchQuery 搜索关键词
 * @returns 是否匹配
 */
const matchesSearchQuery = (nodeData: SubstationNode, searchQuery: string): boolean => {
	if (!nodeData || !searchQuery) return false;

	// 转为小写进行不区分大小写的匹配
	const query = searchQuery.toLowerCase();

	// 匹配名称
	if (nodeData.name && nodeData.name.toLowerCase().includes(query)) {
		return true;
	}

	// 匹配拼音首字母
	if (nodeData.name) {
		const pinyinInitials = getPinyinInitials(nodeData.name);
		if (pinyinInitials.includes(query)) {
			return true;
		}
	}

	// 匹配类型
	if (nodeData.type && nodeData.type.toLowerCase().includes(query)) {
		return true;
	}

	// 匹配电压等级
	if (nodeData.voltageLevel && nodeData.voltageLevel.toLowerCase().includes(query)) {
		return true;
	}

	return false;
};

/**
 * 自定义节点类，支持浮动标签装饰
 * 标签作为装饰元素，不影响节点边界计算
 */
class LabelAdornmentNode extends go.Node {
	// 静态标签模板
	static labelTemplate: go.Adornment | null = null;

	/**
	 * 更新节点装饰，自动添加标签装饰
	 */
	updateAdornments() {
		super.updateAdornments();
		if (LabelAdornmentNode.labelTemplate === null) return;
		
		let ad = this.findAdornment("LABEL");
		if (ad === null) {
			// 每个节点必须有自己的标签模板副本
			ad = LabelAdornmentNode.labelTemplate.copy();
			ad.adornedObject = this;
			this.addAdornment("LABEL", ad);
		}
	}
}

/**
 * 初始化图表
 * 主函数，负责协调各个初始化步骤
 */
const initDiagram = () => {
	if (!diagram.value) return;

	$ = go.GraphObject.make;

	// 创建图表实例并进行基础配置
	myDiagram = $(go.Diagram, diagram.value, {
		initialContentAlignment: go.Spot.Center,
		'undoManager.isEnabled': true,
		'clickCreatingTool.isEnabled': false,
		'draggingTool.isGridSnapEnabled': true,
		layout: $(go.ForceDirectedLayout, {
			maxIterations: 200,
			defaultSpringLength: 150,
			defaultElectricalCharge: 200,
		}),
	});

	// 设置节点模板
	setupNodeTemplates();

	// 设置连接线模板
	setupLinkTemplate();

	// 设置布局参数
	setupLayout();

	// 加载图表数据
	loadGraphData();
};

/**
 * 搜索节点列表
 * 根据关键词过滤节点，用于下拉选项
 * @param query 搜索关键词
 */
const searchNodes = (query: string) => {
	if (query) {
		// 过滤符合条件的节点
		const filteredNodes = allNodes.value.filter((node) => matchesSearchQuery(node, query));

		// 转换为选项格式
		nodeOptions.value = filteredNodes.map((node) => ({
			key: node.key,
			label: node.name,
			type: node.type,
		}));
	} else {
		// 如果查询为空，返回所有节点前50个
		nodeOptions.value = allNodes.value.slice(0, 50).map((node) => ({
			key: node.key,
			label: node.name,
			type: node.type,
		}));
	}
};

/**
 * 处理节点选择事件
 * 当下拉框选中某个节点时，将视图中心移动到该节点并高亮显示
 */
const handleNodeSelect = (key: string) => {
	if (!key || !myDiagram) return;

	// 查找节点并聚焦
	const node = myDiagram.findNodeForKey(key);
	if (node) {
		// 清除之前的选择
		myDiagram.clearSelection();
		// 选中当前节点
		myDiagram.select(node);
		// 将视图中心移动到该节点
		myDiagram.centerRect(node.actualBounds);
		// 添加震动效果
		shakeNode(node);

		// 显示成功信息
		const nodeData = node.data as SubstationNode;
		ElMessage.success(`显示节点: ${nodeData.name}`);
	}
};

const isShowNodeInfo = ref(false);
const nodeInfo = ref({});

/**
 * 设置节点模板
 * 创建变电站和发电厂两种类型的节点模板
 *
 * 关键属性说明：
 * - locationSpot: 定义节点在图上的定位点，Center表示使用中心点定位
 * - locationObjectName: 使用名为'SHAPE'的对象作为定位参考
 * - selectionAdorned: 是否启用选中状态的装饰效果
 * - selectionObjectName: 对哪个对象应用选中效果
 * - itemArray/itemTemplate: 用于创建多个圆环，根据电压等级变化
 *
 * 【标签优化说明】
 * 使用LabelAdornmentNode自定义节点类和Adornment装饰系统实现浮动标签：
 * 1. 标签作为装饰元素，不参与节点边界计算
 * 2. 标签显示在前景图层，始终可见
 * 3. 连接线能正确连接到节点的实际形状部分
 * 4. 避免了标签影响节点连接点的问题
 */
const setupNodeTemplates = () => {
	if (!myDiagram || !$) return;

	// 变电站节点模板
	const stationTemplate = $(
		LabelAdornmentNode, // 使用自定义节点类
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'SHAPE',
			// 添加选中状态样式
			selectionAdorned: true,
			selectionObjectName: 'SHAPE',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Auto',
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE,
					strokeWidth: 3,
					strokeDashArray: [10, 5],
				}),
				$(go.Placeholder)
			),
		},
		// 添加双击监听器以支持双击快速导航到匹配节点
		new go.Binding('doubleClick', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				// 在双击时显示节点详细信息或执行其他操作
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					// ElMessage.info(`节点信息: ${nodeData.name}`);
					isShowNodeInfo.value = true;
					nodeInfo.value = nodeData;
				}
			};
		}),
		// 根据电压等级确定外观
		$(
			go.Panel,
			'Spot',
			{
				name: 'MAIN_PANEL',
				isPanelMain: true,
				itemTemplate: $(
					go.Panel,
					'Spot',
					$(
						go.Shape,
						'Circle',
						{
							name: 'SHAPE',
							fill: COLORS.WHITE,
							strokeWidth: 1.5,
						},
						new go.Binding('width', 'diameter'),
						new go.Binding('height', 'diameter'),
						new go.Binding('stroke'),
						new go.Binding('strokeWidth')
					)
				),
			},
			new go.Binding('itemArray', '', (data: SubstationNode) => {
				// 获取电压等级配置
				const voltageLevel = data.voltageLevel || '';
				const config = getVoltageConfig(voltageLevel);

				// 直接返回配置中的圆环配置
				return config.circles.map((e) => {
					return {
						...e,
						diameter: e.diameter * ZOOM,
					};
				});
			})
		)
		// 注意：移除了原来的TextBlock标签，现在使用装饰系统
	);

	// 发电厂模板
	const plantTemplate = $(
		LabelAdornmentNode, // 使用自定义节点类
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'SHAPE',
			// 添加选中状态样式
			selectionAdorned: true,
			selectionObjectName: 'SHAPE',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Auto',
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE, // 使用配置的默认线条颜色替代硬编码值
					strokeWidth: 3,
					strokeDashArray: [10, 5],
				}),
				$(go.Placeholder)
			),
		},
		// 添加双击监听器以支持双击快速导航到匹配节点
		new go.Binding('doubleClick', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				// 在双击时显示节点详细信息或执行其他操作
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					ElMessage.info(`节点信息: ${nodeData.name}`);
				}
			};
		}),
		$(go.Shape, 'Circle', {
			name: 'SHAPE', // 为了locationObjectName引用
			width: 26 * ZOOM,
			height: 26 * ZOOM,
			fill: COLORS.WHITE,
			strokeWidth: 1.5,
			stroke: COLORS.BLACK, // 发电厂固定为黑色
		}),
		$(go.Shape, {
			geometryString: 'M -15 0 C -5 -10 5 10 15 0',
			stroke: COLORS.BLACK,
			strokeWidth: 1.5,
			width: 30,
			height: 15,
			fill: null,
		})
		// 注意：移除了原来的TextBlock标签，现在使用装饰系统
	);

	// 创建模板映射
	const templmap = new go.Map<string, go.Part>();
	templmap.add('station', stationTemplate); // 默认模板为变电站
	templmap.add('plant', plantTemplate); // 发电厂模板
	myDiagram.nodeTemplateMap = templmap;

	// 设置静态标签模板（在前景图层的装饰）
	LabelAdornmentNode.labelTemplate = $(
		go.Adornment,
		'Spot',
		{
			layerName: 'Foreground', // 显示在前景图层
		},
		$(go.Placeholder), // 占位符，表示被装饰的节点
		$(
			go.TextBlock,
			{
				alignment: go.Spot.Top,
				alignmentFocus: go.Spot.Bottom,
				font: 'bold 9px 宋体',
				stroke: COLORS.DEFAULT,
				background: 'rgba(255, 255, 255, 0.8)',
				margin: new go.Margin(2, 2, 2, 2),
				editable: false,
				isMultiline: false,
				visible: true,
				segmentOffset: new go.Point(0, -5), // 向上偏移5个像素
			},
			new go.Binding('text', 'name'),
			new go.Binding('stroke', 'voltageLevel', (voltageLevel: string) => {
				// 使用配置函数获取颜色
				return getVoltageColor(voltageLevel);
			})
		)
	).copyTemplate(true); // 复制模板并准备高效复制
};

/**
 * 设置连接线模板
 * 创建可视化电力连接线的样式
 *
 * 关键属性和方法说明：
 * - ParallelRouteLink: 自定义连线类，支持平行线路的绘制
 * - relinkableFrom/relinkableTo: 允许用户重新连接线的起点/终点
 * - reshapable: 允许用户调整线的形状
 * - curve: 线的曲率类型，None表示直线
 * - layerName: 图层名称，Background表示在背景层
 * - stroke: 线颜色
 * - strokeWidth: 线宽
 * - strokeDashArray: 线型，[0]表示实线，非零数组表示虚线
 * - toArrow: 箭头类型
 * - segmentIndex/segmentFraction: 控制线上元素(如箭头)的位置
 * - segmentOrientation: 线上元素的朝向
 */
const setupLinkTemplate = () => {
	if (!myDiagram || !$) return;

	/**
	 * 获取线路上元素的位置比例
	 * 用于确定箭头和标签在线上的位置
	 * @param data 连接线数据
	 * @returns 位置比例（0-1）
	 */
	const getSegmentFractionForLine = (data: SubstationLink): number => {
		const lineCount = data.lineCount || 1;
		const lineIndex = data.lineIndex || 0;

		// 使用配置函数获取位置比例
		return getLineSegmentFraction(lineCount, lineIndex);
	};

	// 使用 ParallelRouteLink 创建连接线模板
	myDiagram.linkTemplate = new ParallelRouteLink({
		relinkableFrom: true,
		relinkableTo: true,
		reshapable: true,
		curve: go.Link.JumpOver,
		layerName: 'Background',
		// 右键事件绑定
		contextClick: (e: go.InputEvent, link: ParallelRouteLink) => {
			e.handled = true; // 阻止默认右键菜单
			showContextMenu(e, link); // 自定义显示菜单逻辑
		},
	})
		.add(
			// 主线条
			new go.Shape({
				strokeWidth: 2,
				stroke: COLORS.KV_500,
				name: 'SHAPE',
				strokeDashArray: [0], // 实线
			})
				.bind('stroke', '', (data: SubstationLink) => {
					return data.color;
				})
				.bind('strokeDashArray', '', (data: SubstationLink) => {
					return [0]; // 默认为实线
				})
				.bind('strokeWidth', '', (data: SubstationLink) => {
					return getVoltageLineWidth(data.voltageLevel);
				})
		)
		.add(
			// 中间箭头
			new go.Shape({
				toArrow: 'OpenTriangle', // 箭头样式：开放三角形
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5, // 调整箭头尺寸，使其更合适
				segmentIndex: 0,
				segmentFraction: 0.5, // 确保箭头位于线段中间
				segmentOrientation: go.Link.OrientAlong, // 箭头沿线方向
			})
				.bind('stroke', '', (data: SubstationLink) => {
					return data.color;
				})
				.bind('fill', '', (data: SubstationLink) => {
					return data.color;
				})
				.bind('scale', '', (data: SubstationLink) => {
					// 调整箭头大小
					return 1.5;
				})
				// 添加对 segmentIndex 的动态绑定，以适应多线路情况
				.bind('segmentIndex', '', (data: SubstationLink) => {
					// 获取线条数量
					const lineCount = data.lineCount || 1;
					// 对于多条线路，ParallelRouteLink 会插入额外控制点
					// 单线时不需要特殊处理，segmentIndex 保持为 0
					// 多线时需要将 segmentIndex 设为1，因为 computePoints 会在索引1处插入控制点
					return lineCount > 1 ? 1 : 0;
				})
				// 从配置中读取segmentFraction，控制箭头位置
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getSegmentFractionForLine(data);
				})
				// 根据原始方向信息调整箭头方向
				.bind('visible', '', (data: SubstationLink) => {
					// 获取源数据的原始方向
					const sourceId = data.rawData?.source || '';
					const direction = data.rawData?.direction || 'forward';
					
					// 如果线路的物理from节点与源数据的source相同，且源数据方向为forward
					// 或者线路的物理from节点与源数据的source不同，且源数据方向为backward
					const showForwardArrow = 
						(data.from === sourceId && direction === 'forward') || 
						(data.from !== sourceId && direction === 'backward');
					
					return showForwardArrow;
				})
		)
		.add(
			// 反方向箭头（根据方向显示）
			new go.Shape({
				fromArrow: 'BackwardOpenTriangle', // 反向箭头样式
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5, // 调整箭头尺寸，使其更合适
				segmentIndex: 0,
				segmentFraction: 0.5, // 确保箭头位于线段中间
				segmentOrientation: go.Link.OrientAlong, // 箭头沿线方向
			})
				.bind('stroke', '', (data: SubstationLink) => {
					return data.color;
				})
				.bind('fill', '', (data: SubstationLink) => {
					return data.color;
				})
				.bind('scale', '', (data: SubstationLink) => {
					// 调整箭头大小
					return 1.5;
				})
				// 添加对 segmentIndex 的动态绑定，以适应多线路情况
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				// 从配置中读取segmentFraction，控制箭头位置
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getSegmentFractionForLine(data);
				})
				// 根据原始方向信息调整箭头方向
				.bind('visible', '', (data: SubstationLink) => {
					// 获取源数据的原始方向
					const sourceId = data.rawData?.source || '';
					const direction = data.rawData?.direction || 'forward';
					
					// 如果线路的物理from节点与源数据的source相同，且源数据方向为backward
					// 或者线路的物理from节点与源数据的source不同，且源数据方向为forward
					const showBackwardArrow = 
						(data.from === sourceId && direction === 'backward') || 
						(data.from !== sourceId && direction === 'forward');
					
					return showBackwardArrow;
				})
		)
		.add(
			// 数值标签
			new go.TextBlock({
				segmentOffset: new go.Point(10, -10), // 调整标签偏移到线的上方
				font: '9px 宋体',
				stroke: COLORS.DEFAULT,
				background: 'rgba(255, 255, 255, 0.9)', // 半透明白色背景提高可读性
				segmentOrientation: go.Link.OrientUpright, // 保持文本正向显示
			})
				.bind('text', '', (data: SubstationLink) => {
					return  `${data.rawData.properties.p_to_mw}  ${data.rawData.name}  ${data.rawData.properties.p_from_mw}`;
				})
				// 添加与箭头相同的 segmentIndex 动态绑定
				.bind('segmentIndex', '', (data: SubstationLink) => {
					// 获取线条数量
					const lineCount = data.lineCount || 1;
					// 与箭头使用相同的逻辑
					return lineCount > 1 ? 1 : 0;
				})
				// 从配置中读取segmentFraction，控制标签位置
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getSegmentFractionForLine(data);
				})
				// 添加根据电压等级调整文本颜色
				.bind('stroke', '', (data: SubstationLink) => {
					return data.color;
				})
		);
};
// 新增开始
// 显示右键菜单
function showContextMenu(e: go.InputEvent, link: go.Link) {
	currentLink = link;
	menuStyle.value = {
		left: `${e.viewPoint.x}px`,
		top: `${e.viewPoint.y}px`
	};
	lineRightName.value = currentLink.data.rawData.properties.in_service === 0 ? '投运' : '停运';
	gridLineVisible.value = true;
}
// 点击外部关闭菜单
function handleClickOutside(e: MouseEvent) {
	if (menuRef.value && !menuRef.value.contains(e.target as Node)) {
		gridLineVisible.value = false;
	}
}
// 新增结束
/**
 * 设置图表布局
 * 配置力导向布局参数
 *
 * 关键参数说明：
 * - defaultSpringLength: 弹簧默认长度，影响节点间距
 * - defaultElectricalCharge: 电荷强度，影响节点排斥力
 * - maxIterations: 最大迭代次数，影响布局计算复杂度
 * - isInitial: 是否应用初始布局
 * - isOngoing: 是否持续优化布局
 */
const setupLayout = () => {
	if (!myDiagram) return;

	// 获取并优化布局参数
	const layout = myDiagram.layout as go.ForceDirectedLayout;

	// 优化布局参数以获得更好的自动布局效果
	layout.defaultSpringLength = 250; // 增加弹簧长度使节点间距更大
	layout.defaultElectricalCharge = 350; // 增加电荷排斥力
	layout.maxIterations = 300;

	// 启用自动布局
	myDiagram.layout.isInitial = true; // 初始布局
	myDiagram.layout.isOngoing = true; // 持续布局
};

/**
 * 将后端API数据转换为GoJS可用的节点数据
 * 转换过程中进行数据规范化和类型适配
 *
 * 关键字段说明：
 * - key: 节点唯一标识，对应后端id或graph_node_id
 * - name: 节点名称，对应后端label
 * - type: 节点类型(station/plant)
 * - voltageLevel: 电压等级，影响节点样式
 *
 * @param apiData API返回的原始数据
 * @returns 转换后的NodeData数组
 */
const convertToNodeData = (apiData: any[]): SubstationNode[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	const result = apiData.map((item) => {
		const nodeData: SubstationNode = {
			key: item.id,
			name: item.name,
			type: item.type,
			category:determineNodeType(item.type),
			voltageLevel: item.voltage || '',
			rawData: item, 
		};

		return nodeData;

	});

	allNodes.value = result;
	
	return result;
};

/**
 * 确定节点类型
 * 根据名称或子类型判断是发电厂还是变电站
 *
 * @param type 类型
 * @returns 节点类型: 'station'(变电站) 或 'plant'(发电厂)
 */
const determineNodeType = (type: string): 'station' | 'plant' => {
	if (!type) return 'station';

	if (['变电站', '换流站', '开关站', '牵引站'].includes(type)) {
		return 'station';
	} else {
		return 'plant';
	}
};

/**
 * 将后端API数据转换为GoJS可用的连接线数据
 * 处理多条平行线路的分组和属性设置
 *
 * 关键字段说明：
 * - from/to: 连线的起点和终点节点ID
 * - value: 显示在连线上的值，通常是功率
 * - direction: 电力流向，forward或backward
 * - lineCount: 相同起终点的线路总数
 * - lineIndex: 当前线路在组内的索引
 * - points: 路径点数组，用于自定义线路形状
 *
 * @param apiData API返回的原始数据
 * @returns 转换后的LinkData数组
 */
const convertToLinkData = (apiData: any[]): SubstationLink[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	// 步骤1: 收集相同起点和终点的线路
	const routeGroups = new Map<string, any[]>();

	apiData.forEach((item) => {
		const fromId = item.source || '';
		const toId = item.target || '';

		// 对起点和终点ID进行排序，确保无论方向如何，连接相同节点对的线路使用相同的路由键
		const [sortedId1, sortedId2] = [fromId, toId].sort();
		
		// 创建路由键（排序后的节点ID对）
		const routeKey = `${sortedId1}-${sortedId2}`;

		// 将线路添加到对应的组
		if (!routeGroups.has(routeKey)) {
			routeGroups.set(routeKey, []);
		}
		routeGroups.get(routeKey)!.push(item);
	});

	// 步骤2: 为每组线路分配适当的lineCount和lineIndex
	const result: SubstationLink[] = [];

	routeGroups.forEach((group, routeKey) => {
		// 使用配置中的最大线路数
		const lineCount = Math.min(group.length, MAX_PARALLEL_LINES);
		
		// 从路由键中提取排序后的节点ID
		const [sortedId1, sortedId2] = routeKey.split('-');

		group.forEach((item, index) => {
			// 超过最大线路数的忽略
			if (index >= MAX_PARALLEL_LINES) return;

			// 处理lineList字符串，转换为点数组
			let points: Point[] = [];
			if (item.lineList) {
				try {
					// 尝试解析字符串为JSON
					const lineListData = JSON.parse(item.lineList);
					// 如果是数组，直接使用
					if (Array.isArray(lineListData)) {
						points = lineListData.map((point) => ({
							x: parseFloat(point.x || 0),
							y: parseFloat(point.y || 0),
						}));
					}
				} catch (error) {
					console.error('解析lineList失败:', error);
				}
			}

			// 获取当前线路的起终点
			const fromId = item.source || '';
			const toId = item.target || '';

			// 确定是否需要调整方向，以使组内所有线路方向一致
			// 统一让所有线路从sortedId1指向sortedId2
			const needSwap = (fromId !== sortedId1);
			
			// 如果需要交换节点，同时也需要处理方向
			let direction = item.direction || 'forward';
			// direction 保持原样，作为箭头显示的依据

			// 创建连接线数据
			const linkData: SubstationLink = {
				// 如果需要交换节点，则交换from/to，确保所有线路方向一致
				from: sortedId1, // 统一使用排序后的第一个ID作为起点
				to: sortedId2,   // 统一使用排序后的第二个ID作为终点
				color: determineLineColor(item), // 线路颜色
				voltageLevel: item.voltage,
				direction: direction, // 原始方向信息
				lineCount: lineCount, // 设置为当前组的线条数
				lineIndex: index, // 线路索引
				points: needSwap ? [...points].reverse() : points, // 如果交换节点，路径点也需要反转
				rawData: item, // 保存原始数据
			};
			result.push(linkData);
		});
	});

	return result;
};

/**
 * 确定线路颜色
 * 根据线路属性(如电压等级、负载率)确定颜色
 *
 * @param item 线路数据项
 * @returns 颜色值，如'#1890ff'
 */
const determineLineColor = (item: any): string => {
	// 如果有电压等级信息，根据电压等级确定颜色
	if (item.voltage) {
		return getVoltageColor(item.voltage);
	}
	// 使用配置的默认线条颜色
	return COLORS.DEFAULT_LINE;
};

/**
 * 加载图表数据
 * 可以从API获取数据或使用模拟数据
 *
 * 处理流程:
 * 1. 获取API数据或使用模拟数据
 * 2. 转换为GoJS可用的格式
 * 3. 创建图表模型并应用数据
 * 4. 触发图表布局
 *
 * @param useApi 是否使用API获取数据
 * @param nodeData 可选的节点数据，如果不提供则使用模拟数据
 * @param linkData 可选的连接线数据，如果不提供则使用模拟数据
 */
 const nodeData1=[
{
                "id": "300801.0",
                "name": "甘华亭电厂",
                "type": "变电站",
                "voltage": "110kV",
                "properties": {
    "U": 561.838,
    "U(M)": 533.645
}
            },
{
                "id": "301420.0",
                "name": "甘西华变电站",
                "type": "变电站",
                "voltage": "110kV",
                "properties": 
								{
    "U": 561.838,
    "U(M)": 533.645
}
            },
						{
                "id": "301844.0",
                "name": "甘安西变电站",
                "type": "变电站",
                "voltage": "110kV",
                "properties": 
								{
    "U": 561.838,
    "U(M)": 533.645
}
            },
						{
                "id": "300780.0",
                "name": "甘瓜州变电站",
                "type": "变电站",
                "voltage": "330kV",
                "properties": 
								{
    "U": 561.838,
    "U(M)": 533.645
}
            },
]
const data1=[
{
                "source": "301420.0",
                "target": "300801.0",
                "name": "AC722179",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
								}
            },
            {
                "source": "301420.0",
                "target": "300801.0",
                "name": "AC722180",
                "voltage": "110kV",
                "direction": "forward",
                "properties": 
								{
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
            {
                "source": "300801.0",
                "target": "301420.0",
                "name": "AC722181",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
            {
                "source": "300801.0",
                "target": "301420.0",
                "name": "AC722182",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
						{
                "source": "301844.0",
                "target": "300780.0",
                "name": "AC722039",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
            {
                "source": "301844.0",
                "target": "300780.0",
                "name": "AC722040",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
            {
                "source": "301844.0",
                "target": "300780.0",
                "name": "AC722041",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
									"max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
            {
                "source": "301844.0",
                "target": "300780.0",
                "name": "AC722042",
                "voltage": "110kV",
                "direction": "forward",
                "properties": {
    "max_i_ka": 3.26,
    "p_from_mw": -1.36,
    "q_from_mvar": -366.63,
    "p_to_mw": 2.01,
    "q_to_mva": 269.10,
    "pl_mw": 0.65,
    "ql_mvar": -97.52,
    "i_from_ka": 0.38,
    "i_to_ka": 0.27,
    "i_ka": 0.38,
    "vm_from_pu": 1.05,
    "va_from_degree": 0.07,
    "vm_to_pu": 1.07,
    "va_to_degree": -0.03,
    "loading_percent": 11.70
}
            },
]

const isLoading = ref(false);
const loadGraphData = async (useApi = true, nodeData?: SubstationNode[], linkData?: SubstationLink[]) => {
	if (!myDiagram) return;
	isLoading.value = true;
	try {
		 const { data } = await query_substation_graph({ bb_case_id: dataPacket.value.id, bb_diagram_layer_id: '34' });

		// 提取节点和连接线数据
		const apiNodes = data.nodes || [];
		const apiLinks = data.edges || [];

		// 转换为GoJS可用的格式
		nodeData = convertToNodeData(apiNodes);
		linkData = convertToLinkData(apiLinks);
	} catch (error) {
		console.error('加载API数据失败:', error);
		ElMessage.error('加载API数据失败，使用模拟数据');
	} finally {
		isLoading.value = false;
	}
	// 设置图表模型
	myDiagram.model = new go.GraphLinksModel(nodeData, linkData);

	// 加载完数据后触发布局
	myDiagram.layoutDiagram(true);
};

/**
 * 保存图表数据到本地存储
 * 将当前图表状态转换为JSON字符串保存
 */
const saveDiagram = () => {
	if (!myDiagram) return;
	const json = myDiagram.model.toJson();
	localStorage.setItem('diagramData', json);
	ElMessage.success('图表已保存');
};

/**
 * 从本地存储加载图表数据
 * 从localStorage读取JSON数据并应用到图表
 */
const loadDiagram = () => {
	if (!myDiagram) return;
	const json = localStorage.getItem('diagramData');
	if (json) {
		myDiagram.model = go.Model.fromJson(json);
		ElMessage.success('图表已加载');
	} else {
		ElMessage.warning('没有找到保存的图表数据');
	}
};

/**
 * 为节点添加震动效果，吸引用户注意
 * 通过短时间内随机改变节点位置实现震动效果
 *
 * @param node 要添加效果的节点
 */
const shakeNode = (node: any) => {
	if (!node || !myDiagram) return;

	// 保存原始位置
	const origLoc = node.location.copy();

	// 震动计数
	let count = 0;

	// 创建震动效果
	const interval = setInterval(() => {
		// 震动8次后停止
		if (count >= 8) {
			clearInterval(interval);
			node.location = origLoc; // 恢复原始位置
			return;
		}

		// 计算随机偏移
		const offsetX = (Math.random() - 0.5) * 5;
		const offsetY = (Math.random() - 0.5) * 5;

		// 应用新位置
		node.location = new go.Point(origLoc.x + offsetX, origLoc.y + offsetY);

		count++;
	}, 50);
};

/**
 * 初始化缩略图
 * 创建一个与主图表关联的缩略图
 */
const initOverview = () => {
	if (!overviewRef.value || !myDiagram || !$) return;

	// 创建缩略图实例
	myOverview = $(go.Overview, overviewRef.value, {
		observed: myDiagram, // 与主图表关联
		contentAlignment: go.Spot.Center, // 内容居中
		box: $(
			go.Part, // 自定义视口框样式
			$(go.Shape, {
				fill: 'rgba(100, 149, 237, 0.2)', // 半透明蓝色
				stroke: 'cornflowerblue',
				strokeWidth: 2,
			})
		),
	});
};
// 新增开始
// 修改线路有效性
const updateService = () => {
	let info = currentLink ? currentLink.data.rawData.properties : {};
	ElMessageBox.confirm(
		`确定要${lineRightName.value}该线路吗？`,
		'提示',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		}
	)
		.then(() => {
			isLoading.value = true
			let queryLine = {
				line_id: info.ubid,
				in_service: info.in_service === 1 ? '0' : '1',
				bb_case_id: dataPacket.value.id,
				//section_id: this.section_id,
				//dispatch_flows: this.$refs.leftMenuRef.dispatchFlowCheck_temp
			}
			// 假设 res 的类型为某种响应类型，这里定义一个示例类型
			type SaveDispatchFlowResponse = {
				data: {
					df_ids: any; // 假设 df_ids 是任意类型，可根据实际情况修改
				}
			};
			gridLineVisible.value = false
			isLoading.value = false
			let df_ids = ['f03079af-e6d5-4f65-8d77-b7606ea5058b', 'b0fdc2dc-9bed-47a0-ae7e-9f188b9ff74e','347ab8c2-78f3-4195-ba24-4c98a5b54264']
			emit('lineChange', currentLink ? currentLink.data.rawData : {},[])
			initDiagram();
			initOverview();
			// calculateApi.saveDispatchFlow(queryLine).then((res: SaveDispatchFlowResponse) => {
			// 	isLoading.value = false
			// 	emit('lineChange', res.data.df_ids)
			// }).catch(() => {
			// 	isLoading.value = false
			// })
		})
		.catch(() => {
			isLoading.value = false
		})
}
// 新增结束
// 组件挂载时初始化图表
onMounted(() => {
	initDiagram();
	initOverview();
	// 新增开始
	// 绑定全局点击事件
	document.addEventListener('click', handleClickOutside);
	// 新增结束
});
// 新增开始
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
// 新增结束
</script>
<!-- css均为新增 -->
<style scoped>
.contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
    border: 1px solid #005aff;
  }

  .contextmenu li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
  }

  .contextmenu li:hover {
    background: #eee;
  }
</style>
