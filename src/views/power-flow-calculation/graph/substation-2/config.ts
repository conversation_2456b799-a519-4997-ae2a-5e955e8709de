/**
 * 电压等级配置文件
 * 集中管理所有电压等级的配置和颜色常量
 */

// 颜色常量定义
export const COLORS = {
	// 基础颜色
	DEFAULT: '#333',
	DEFAULT_LINE: '#1890ff',
	WHITE: 'white',
	BLACK: '#000',

	// 电压等级颜色
	KV_10_BELOW: '#99CCFF', // 浅蓝色: 10kV以下
	KV_10: '#0066CC', // 蓝色: 10kV
	KV_35: '#003366', // 深蓝色: 35kV
	KV_110: '#006600', // 深绿色: 110kV
	KV_220: '#33CC33', // 浅绿色: 220kV
	KV_330: '#FF9900', // 橙色: 330kV
	KV_500: '#FF0000', // 红色: 500kV
	KV_750: '#990000', // 暗红色: 750kV
	KV_800: '#800080', // 紫色: 1000kV
	KV_1000: '#800080', // 紫色: 1000kV

	// 状态颜色
	STATUS_WARNING: 'orange',
	STATUS_OFFLINE: 'gray',
	STATUS_MAINTENANCE: 'purple',

	// 通道类型颜色
	CHANNEL_BACKUP: 'green',
	CHANNEL_EMERGENCY: 'blue',
};

export const ZOOM = 1.8;

// 圆环配置接口
export interface CircleConfig {
	diameter: number;
	stroke: string;
	strokeWidth: number;
}

// 电压等级配置接口
export interface VoltageConfig {
	name: string; // 电压等级名称
	key: string; // 搜索用的关键字
	color: string; // 主要颜色
	circles: CircleConfig[]; // 圆环配置
	lineWidth?: number; // 连接线宽度
}

// 电压等级配置表
export const VOLTAGE_CONFIGS: { [key: string]: VoltageConfig } = {
	// 10kV以下
	KV_10_BELOW: {
		name: '10kV以下',
		key: '10kV以下',
		color: COLORS.KV_10_BELOW,
		circles: [{ diameter: 14, stroke: COLORS.KV_10_BELOW, strokeWidth: 1.5 }],
		lineWidth: 1,
	},

	// 10kV
	KV_10: {
		name: '10kV',
		key: '10kV',
		color: COLORS.KV_10,
		circles: [{ diameter: 15, stroke: COLORS.KV_10, strokeWidth: 1.5 }],
		lineWidth: 1,
	},

	// 35kV
	KV_35: {
		name: '35kV',
		key: '35',
		color: COLORS.KV_35,
		circles: [{ diameter: 16, stroke: COLORS.KV_10, strokeWidth: 1.5 }],
		lineWidth: 1,
	},

	// 110kV
	KV_110: {
		name: '110kV',
		key: '110',
		color: COLORS.KV_110,
		circles: [{ diameter: 20, stroke: COLORS.KV_110, strokeWidth: 1.5 }],
		lineWidth: 2,
	},

	// 220kV
	KV_220: {
		name: '220kV',
		key: '220',
		color: COLORS.KV_220,
		circles: [
			{ diameter: 24, stroke: COLORS.KV_220, strokeWidth: 1.5 },
			{ diameter: 20, stroke: COLORS.KV_220, strokeWidth: 1.5 },
		],
		lineWidth: 2,
	},

	// 330kV
	KV_330: {
		name: '330kV',
		key: '330',
		color: COLORS.KV_330,
		circles: [
			{ diameter: 26, stroke: COLORS.KV_330, strokeWidth: 1.5 },
			{ diameter: 22, stroke: COLORS.KV_330, strokeWidth: 1.5 },
			{ diameter: 18, stroke: COLORS.KV_330, strokeWidth: 1.5 },
		],
		lineWidth: 3,
	},

	// 500kV
	KV_500: {
		name: '500kV',
		key: '500',
		color: COLORS.KV_500,
		circles: [
			{ diameter: 26, stroke: COLORS.KV_500, strokeWidth: 1.5 },
			{ diameter: 22, stroke: COLORS.KV_500, strokeWidth: 1.5 },
			{ diameter: 18, stroke: COLORS.KV_500, strokeWidth: 1.5 },
		],
		lineWidth: 3,
	},

	// 750kV
	KV_750: {
		name: '750kV',
		key: '750',
		color: COLORS.KV_750,
		circles: [
			{ diameter: 36, stroke: COLORS.KV_750, strokeWidth: 1.5 },
			{ diameter: 32, stroke: COLORS.KV_750, strokeWidth: 1.5 },
			{ diameter: 28, stroke: COLORS.KV_750, strokeWidth: 1.5 },
			{ diameter: 24, stroke: COLORS.KV_750, strokeWidth: 1.5 },
		],
		lineWidth: 4,
	},

	// 800kV
	KV_800: {
		name: '800kV',
		key: '800',
		color: COLORS.KV_800,
		circles: [
			{ diameter: 36, stroke: COLORS.KV_800, strokeWidth: 1.5 },
			{ diameter: 32, stroke: COLORS.KV_800, strokeWidth: 1.5 },
			{ diameter: 28, stroke: COLORS.KV_800, strokeWidth: 1.5 },
			{ diameter: 24, stroke: COLORS.KV_800, strokeWidth: 1.5 },
			{ diameter: 20, stroke: COLORS.KV_800, strokeWidth: 1.5 },
		],
		lineWidth: 4,
	},

	// 1000kV
	KV_1000: {
		name: '1000kV',
		key: '1000',
		color: COLORS.KV_1000,
		circles: [
			{ diameter: 36, stroke: COLORS.KV_1000, strokeWidth: 1.5 },
			{ diameter: 32, stroke: COLORS.KV_1000, strokeWidth: 1.5 },
			{ diameter: 28, stroke: COLORS.KV_1000, strokeWidth: 1.5 },
			{ diameter: 24, stroke: COLORS.KV_1000, strokeWidth: 1.5 },
			{ diameter: 20, stroke: COLORS.KV_1000, strokeWidth: 1.5 },
		],
		lineWidth: 4,
	},

	// 默认/未知电压等级
	DEFAULT: {
		name: '未知',
		key: '',
		color: COLORS.KV_35,
		circles: [{ diameter: 16, stroke: COLORS.KV_10, strokeWidth: 1.5 }],
		lineWidth: 1,
	},
};

/**
 * 获取电压等级配置
 * @param voltageLevel 电压等级字符串
 * @returns 对应的电压等级配置
 */
export function getVoltageConfig(voltageLevel: string): VoltageConfig {
	if (!voltageLevel) return VOLTAGE_CONFIGS.DEFAULT;
	// console.log(voltageLevel, 'voltageLevel');
	// 优先进行精确匹配
	for (const key in VOLTAGE_CONFIGS) {
		if (voltageLevel === VOLTAGE_CONFIGS[key].name) {
			return VOLTAGE_CONFIGS[key];
		}
	}

	// 如果没有精确匹配，尝试关键字匹配
	for (const key in VOLTAGE_CONFIGS) {
		if (voltageLevel.includes(VOLTAGE_CONFIGS[key].key)) {
			return VOLTAGE_CONFIGS[key];
		}
	}

	// 未找到匹配配置，返回默认配置
	return VOLTAGE_CONFIGS.DEFAULT;
}

/**
 * 获取电压等级对应的颜色
 * @param voltageLevel 电压等级
 * @returns 对应的颜色值
 */
export function getVoltageColor(voltageLevel: string): string {
	return getVoltageConfig(voltageLevel).color;
}

/**
 * 获取电压等级线宽
 * @param voltageLevel 电压等级
 * @returns 线宽
 */
export function getVoltageLineWidth(voltageLevel: string): number {
	return getVoltageConfig(voltageLevel).lineWidth || 1;
}

// 最大支持的平行线路数
export const MAX_PARALLEL_LINES = 4;

// 平行线路配置
export interface ParallelLineConfig {
	curviness: number; // 曲率
	segmentFraction: number; // 线上元素位置比例
}

// 平行线路配置对象
export interface ParallelLinesConfig {
	[lineCount: number]: {
		// 线路总数
		[lineIndex: number]: ParallelLineConfig; // 每条线的配置
	};
}

// 线路配置定义
export const PARALLEL_LINE_CONFIG: ParallelLinesConfig = {
	// 单线配置
	1: {
		0: { curviness: 0, segmentFraction: 0.5 },
	},
	// 双线配置
	2: {
		0: { curviness: -20, segmentFraction: 1 },
		1: { curviness: 20, segmentFraction: 1 },
	},
	// 三线配置
	3: {
		0: { curviness: -30, segmentFraction: 0.5 },
		1: { curviness: 0, segmentFraction: 1 },
		2: { curviness: 30, segmentFraction: 0.5 },
	},
	// 四线配置
	4: {
		0: { curviness: -70, segmentFraction: 1 },
		1: { curviness: -25, segmentFraction: 1 },
		2: { curviness: 25, segmentFraction: 1 },
		3: { curviness: 70, segmentFraction: 1 },
	},
};

/**
 * 获取线路曲率配置
 * @param lineCount 线路总数
 * @param lineIndex 当前线路索引
 * @returns 曲率值
 */
export function getLineCurviness(lineCount: number, lineIndex: number): number {
	if (PARALLEL_LINE_CONFIG[lineCount] && PARALLEL_LINE_CONFIG[lineCount][lineIndex]) {
		return PARALLEL_LINE_CONFIG[lineCount][lineIndex].curviness;
	}
	return 0;
}

/**
 * 获取线路上元素位置配置
 * @param lineCount 线路总数
 * @param lineIndex 当前线路索引
 * @returns 位置比例
 */
export function getLineSegmentFraction(lineCount: number, lineIndex: number): number {
	if (PARALLEL_LINE_CONFIG[lineCount] && PARALLEL_LINE_CONFIG[lineCount][lineIndex]) {
		return PARALLEL_LINE_CONFIG[lineCount][lineIndex].segmentFraction;
	}
	return 0.5;
}
