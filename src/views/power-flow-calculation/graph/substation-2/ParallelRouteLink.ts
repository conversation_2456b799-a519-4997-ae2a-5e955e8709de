﻿/*
 *  Copyright 1998-2025 by Northwoods Software Corporation. All Rights Reserved.
 */

/*
 * This is an extension and not part of the main GoJS library.
 * The source code for this is at extensionsJSM/ParallelRouteLink.ts.
 * Note that the API for this class may change with any version, even point releases.
 * If you intend to use an extension in production, you should copy the code to your own source directory.
 * Extensions can be found in the GoJS kit under the extensions or extensionsJSM folders.
 * See the Extensions intro page (https://gojs.net/latest/intro/extensions.html) for more information.
 */

import * as go from 'gojs';

/**
 * This custom {@link go.Link} class customizes its route to go parallel to other links connecting the same ports,
 * if the link is not orthogonal and is not Bezier curved.
 *
 * If you want to experiment with this extension, try the <a href="../../samples/ParallelRoute.html">Parallel Route Links</a> sample.
 * @category Part Extension
 */
export class ParallelRouteLink extends go.Link {
	constructor(init?: Partial<ParallelRouteLink>) {
		super();
		if (init) Object.assign(this, init);
	}

	/**
	 * Constructs the link's route by modifying {@link points}.
	 * @returns true if it computed a route of points
	 */
	override computePoints(): boolean {
		const result = super.computePoints();
		if (!this.isOrthogonal && this.curve !== go.Curve.Bezier && this.hasCurviness()) {
			const curv = this.computeCurviness();

			// 获取线路数据
			const linkdata = this.data;
			const lineCount = linkdata?.lineCount || 1;

			// 如果没有指定曲率，则不执行偏移计算
			if (curv !== 0) {
				// 获取线段的端点
				const num = this.pointsCount;
				let pidx = 0;
				let qidx = num - 1;
				if (num >= 4) {
					pidx++;
					qidx--;
				}

				const frompt = this.getPoint(pidx); // 起始点
				const topt = this.getPoint(qidx); // 终点
				const dx = topt.x - frompt.x; // x方向差值
				const dy = topt.y - frompt.y; // y方向差值

				// 计算控制点比例因子 - 根据线路数量调整
				// 增加线路数量时，使控制点更接近端点，增强曲线效果
				const frontRatio = lineCount > 2 ? 1 / 6 : 1 / 8; // 多线路时减小前控制点比例
				const backRatio = lineCount > 2 ? 5 / 6 : 7 / 8; // 多线路时增大后控制点比例

				// 计算线路中间控制点的位置（使用动态比例）
				let mx = frompt.x + dx * frontRatio;
				let my = frompt.y + dy * frontRatio;
				let px = mx;
				let py = my;

				// 处理水平线的特殊情况
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) py -= curv;
					else py += curv;
				} else {
					// 非水平线的偏移计算
					const slope = -dx / dy; // 垂直于原线段的斜率
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					px = (dy < 0 ? -1 : 1) * e + mx;
					py = slope * (px - mx) + my;
				}

				// 计算线路中间控制点的位置（使用动态比例）
				mx = frompt.x + dx * backRatio;
				my = frompt.y + dy * backRatio;
				let qx = mx;
				let qy = my;

				// 与上面相同的逻辑，处理线段终点附近的控制点
				if (-0.01 < dy && dy < 0.01) {
					if (dx > 0) qy -= curv;
					else qy += curv;
				} else {
					const slope = -dx / dy;
					let e = Math.sqrt((curv * curv) / (slope * slope + 1));
					if (curv < 0) e = -e;
					qx = (dy < 0 ? -1 : 1) * e + mx;
					qy = slope * (qx - mx) + my;
				}

				// 插入计算出的控制点，形成曲线路径
				this.insertPointAt(pidx + 1, px, py);
				this.insertPointAt(qidx + 1, qx, qy);
			}
		}
		return result;
	}

	/**
	 * 计算曲率值以实现平行线路的均匀分布
	 * 使用固定宽度（100）平均分配给各条线路
	 * @returns 计算后的曲率值
	 */
	override computeCurviness(): number {
		// 获取连接数据
		const linkdata = this.data;
		if (linkdata) {
			// 线路总数，默认为1
			const lineCount = linkdata.lineCount || 1;
			// 当前线条索引，默认为0（第一条线）
			const lineIndex = linkdata.lineIndex || 0;

			// 固定总宽度为100，所有线路平分这个宽度
			const FIXED_WIDTH = 100;

			// 根据线条总数和当前索引计算曲率偏移量
			if (lineCount === 1) {
				// 单线情况，居中显示
				return 0;
			} else if (lineCount === 2) {
				// 双线情况：平分宽度，每条线偏移 FIXED_WIDTH/4
				// 这样两条线之间的距离就是 FIXED_WIDTH/2
				return lineIndex === 0 ? -FIXED_WIDTH / 4 : FIXED_WIDTH / 4;
			} else if (lineCount === 3) {
				// 三线情况：平分宽度，均匀分布
				if (lineIndex === 0) return -FIXED_WIDTH / 3;
				if (lineIndex === 1) return 0;
				if (lineIndex === 2) return FIXED_WIDTH / 3;
			} else if (lineCount === 4) {
				// 四线情况：平分宽度，均匀分布
				if (lineIndex === 0) return (-FIXED_WIDTH * 3) / 8; // 最左侧线
				if (lineIndex === 1) return -FIXED_WIDTH / 8; // 左中线
				if (lineIndex === 2) return FIXED_WIDTH / 8; // 右中线
				if (lineIndex === 3) return (FIXED_WIDTH * 3) / 8; // 最右侧线
			} else {
				// 更多线路的情况，使用通用公式计算
				// 将 lineIndex 从 0-(lineCount-1) 映射到 -0.5-0.5 的范围
				const normalizedIndex = lineIndex / (lineCount - 1) - 0.5;
				// 计算曲率，最大可达到 ±(FIXED_WIDTH/2)
				return normalizedIndex * FIXED_WIDTH;
			}
		}

		// 默认返回0曲率（直线）
		return 0;
	}
}
