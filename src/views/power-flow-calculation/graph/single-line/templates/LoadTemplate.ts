import * as go from 'gojs';
import { createPortStyle, createNodeShape, createNodeLabel, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 负荷节点模板
 */

/**
 * 创建负荷节点模板
 * @returns GoJS Node模板
 */
export const createLoadTemplate = () => {
	const $ = go.GraphObject.make;

	// 负荷的几何路径（三角形）
	const loadPath = 'M232.96 363.52h563.2v307.2h-563.2v-307.2z' + 'M232.96 368.64l558.080 296.96' + 'M232.96 665.6l558.080-296.96';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(loadPath, '#1c57ea', 1.5, 11, 6),
			// 文本标签部分
			createNodeLabel({
				alignment: new go.Spot(0.5, 1, 0, 20),
			}),
			// 添加端口 - 负荷只能连入，不能连出
			createPortStyle('O', new go.Spot(0.5, 0), true, true, 0, 1) // 顶部端口，只能连入
		)
	);
};
