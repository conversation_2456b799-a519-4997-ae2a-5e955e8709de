import * as go from 'gojs';
import { createPortStyle, createNodeShape, createNodeLabel, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 线端节点模板
 */

/**
 * 创建线端节点模板
 * @returns GoJS Node模板
 */
export const createACLineDotTemplate = () => {
	const $ = go.GraphObject.make;

	// 线端的几何路径（圆形）
	const dotPath =
		'M512 0c-282.77 0-512 229.23-512 512s229.23 512 512 512 512-229.23 512-512-229.23-512-512-512zM512 896c-212.078 0-384-171.922-384-384s171.922-384 384-384c212.078 0 384 171.922 384 384s-171.922 384-384 384zM320 512c0-106.039 85.961-192 192-192s192 85.961 192 192c0 106.039-85.961 192-192 192s-192-85.961-192-192z';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(dotPath, '#1c57ea', 1.5, 4, 4),
			// 添加端口 - 确保端口在文本标签之前创建，避免覆盖
			createPortStyle('J', new go.Spot(0.5, 0), true, true, 1, 1), // 顶部端口
			// 文本标签部分 - 调整位置避免与端口冲突
			createNodeLabel({
				alignment: new go.Spot(0.5, 1, 0, 15), // 增加偏移量，避免与端口重叠
			})
		)
	);
};
