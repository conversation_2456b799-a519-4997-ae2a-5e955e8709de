import * as go from 'gojs';
import { create<PERSON>ort<PERSON>tyle, createNodeShape, createNode<PERSON>abel, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 交流线路节点模板
 */

/**
 * 创建交流线路节点模板
 * @returns GoJS Node模板
 */
export const createAclineTemplate = () => {
	const $ = go.GraphObject.make;

	// 交流线路的几何路径
	const aclinePath =
		'F M252.416 570.88c0-33.932 27.508-61.44 61.44-61.44s61.44 27.508 61.44 61.44v-0 ' +
		'M650.752 572.928c4.82-34.459 34.099-60.69 69.504-60.69s64.684 26.232 69.461 60.32l0.043 0.37 ' +
		'M377.344 572.928c0-37.184 30.144-67.328 67.328-67.328s67.328 30.144 67.328 67.328v0 ' +
		'M512 572.928c-0.082-1.243-0.128-2.695-0.128-4.158 0-37.326 30.258-67.584 67.584-67.584s67.584 30.258 67.584 67.584c0 1.463-0.046 2.914-0.138 4.354l0.010-0.197';

	return $(
		go.Node,
		'Spot',
		createBaseNodeConfig(),
		...createBaseNodeBindings(),
		createMainPanel(
			// 图标部分
			createNodeShape(aclinePath, '#1c57ea', 1.5, 22, 3),
			// 文本标签部分
			createNodeLabel(),
			// 添加端口
			createPortStyle('J', new go.Spot(0, 0.5), true, true, 1, 1), // 左端口
			createPortStyle('O', new go.Spot(1, 0.5), true, true, 1, 1) // 右端口
		)
	);
};
