import * as go from 'gojs';
import { createTransformerCoil, createNodeLabel, createBaseNodeConfig, createBaseNodeBindings, createMainPanel } from './BaseTemplate';

/**
 * 两绕组变压器节点模板
 *
 * 功能说明:
 * - 由两个独立的圆圈组成，分别代表一次侧和二次侧绕组
 * - 每个圆圈都有独立的端口，可以单独连接线路
 * - 支持每个绕组的独立颜色配置
 * - 端口位置精确匹配圆圈位置，确保连线视觉效果
 */

/**
 * 创建两绕组变压器节点模板
 * @returns GoJS Node模板，包含两个独立的变压器绕组
 */
export const createTrafoTemplate = () => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Spot', // 使用Spot布局，支持精确定位子元素
		createBaseNodeConfig(), // 应用基础节点配置
		...createBaseNodeBindings(), // 应用基础数据绑定
		createMainPanel(
			// === 一次侧绕组 (上方圆圈) ===
			// 位置: (0.5, 0) = 节点上方中央
			// 端口方向: go.Spot.Top = 连线从上方进出
			// 颜色: '#ce4878' = 粉红色 (用户自定义)
			// 尺寸: 20px 直径
			// 端口ID: 'H' (连线时使用此ID)
			createTransformerCoil('H', new go.Spot(0.5, 0), go.Spot.Top, '#ce4878', 20),

			// === 二次侧绕组 (下方圆圈) ===
			// 位置: (0.5, 1) = 节点下方中央
			// 端口方向: go.Spot.Bottom = 连线从下方进出
			// 颜色: '#1c57ea' = 蓝色 (默认颜色)
			// 尺寸: 20px 直径
			// 端口ID: 'L' (连线时使用此ID)
			createTransformerCoil('L', new go.Spot(0.5, 1), go.Spot.Bottom, '#1c57ea', 20),

			// === 节点文本标签 ===
			// 显示变压器名称，位置在节点下方，增加偏移避免与下方端口冲突
			createNodeLabel({
				alignment: new go.Spot(0.5, 1, 0, 20), // 增加偏移量，避免与下方端口重叠
			})
		)
	);
};
