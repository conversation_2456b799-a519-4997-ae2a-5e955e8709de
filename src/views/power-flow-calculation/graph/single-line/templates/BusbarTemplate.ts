import * as go from 'gojs';
import { createPortStyle, createBaseNodeBindings, createNodeLabel } from './BaseTemplate';

/**
 * 创建母线节点模板
 * @returns GoJS Node模板
 */
export const createBusbarTemplate = () => {
	const $ = go.GraphObject.make;

	return $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			selectionAdorned: true, // 选中时显示调整手柄
			selectionObjectName: 'SHAPE', // 选择调整的目标对象
			resizable: true, // 启用调整大小功能
			resizeObjectName: 'SHAPE', // 指定要调整大小的对象
			resizeAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				$(go.Placeholder),
				// 只显示左右两侧的调整手柄，实现水平方向调整
				$(
					go.Shape,
					'Diamond',
					{
						name: 'LeftHandle',
						alignment: new go.Spot(0, 0.5),
						cursor: 'col-resize',
						desiredSize: new go.Size(12, 12),
						fill: 'transparent',
						stroke: 'transparent',
						strokeWidth: 1.5,
					},
					new go.Binding('fill', '', (_, obj) => {
						const adorn = obj.part;
						if (adorn && adorn.adornedObject) {
							// 获取母线的颜色
							return adorn.adornedObject.stroke;
						}
						return 'white';
					}).ofObject(),
					new go.Binding('stroke', '', (_, obj) => {
						const adorn = obj.part;
						if (adorn && adorn.adornedObject) {
							// 获取母线的颜色
							return adorn.adornedObject.stroke;
						}
						return 'white';
					}).ofObject()
				),
				$(
					go.Shape,
					'Diamond',
					{
						name: 'RightHandle',
						alignment: new go.Spot(1, 0.5),
						cursor: 'col-resize',
						desiredSize: new go.Size(12, 12),
						fill: 'transparent',
						stroke: 'transparent',
						strokeWidth: 1.5,
					},
					new go.Binding('fill', '', (_, obj) => {
						const adorn = obj.part;
						if (adorn && adorn.adornedObject) {
							// 获取母线的颜色
							return adorn.adornedObject.stroke;
						}
						return 'white';
					}).ofObject(),
					new go.Binding('stroke', '', (_, obj) => {
						const adorn = obj.part;
						if (adorn && adorn.adornedObject) {
							// 获取母线的颜色
							return adorn.adornedObject.stroke;
						}
						return 'white';
					}).ofObject()
				)
			),
			rotatable: false, // 禁用旋转功能
			// 设置连接点
			fromSpot: go.Spot.AllSides,
			toSpot: go.Spot.AllSides,
			// 移除直接从节点连线的能力，改为从端口连线
			fromLinkable: false,
			toLinkable: false,
			cursor: 'move', // 节点上使用move光标
			movable: true, // 确保节点可以移动
			// 确保允许拖动
			dragComputation: function (node, pt, gridpt) {
				return gridpt; // 返回网格对齐的点
			},
		},
		...createBaseNodeBindings(),
		// 主要内容面板
		$(
			go.Panel,
			'Spot',
			{
				alignment: go.Spot.Center,
				name: 'MAIN_PANEL',
			},
			// 图标部分 - 母线使用LineH形状
			$(
				go.Shape,
				'LineH', // 水平线形状
				{
					name: 'SHAPE', // 添加名称以便于引用
					fill: 'transparent',
					strokeWidth: 5,
					width: 150, // 设置默认宽度为150
					height: 5,
					alignment: go.Spot.Center,
					// 设置最小最大尺寸
					minSize: new go.Size(50, 5), // 最小宽度50
					maxSize: new go.Size(500, 5), // 最大宽度500
					// 鼠标悬停效果，提示可调整大小
					cursor: 'col-resize',
				},
				// 替换简单的颜色绑定为基于电压等级的颜色和线宽绑定
				new go.Binding('stroke', 'color'),
				// 确保width属性与数据模型的width属性正确双向绑定
				new go.Binding('width', 'width', (w) => {
					// 如果width未定义，返回默认值150
					return w === undefined || w === null ? 150 : w;
				}).makeTwoWay()
			),
			// 文本标签部分
			createNodeLabel({
				alignment: new go.Spot(0.5, 1, 0, 15),
			}),
			// 母线中央端口
			createPortStyle('O', new go.Spot(0.5, 0.5), true, true)
		)
	);
};
