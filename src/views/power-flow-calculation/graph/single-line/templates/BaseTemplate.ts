import * as go from 'gojs';

/**
 * 基础模板工具类
 * 提供创建节点模板的通用函数和工具
 */

/**
 * 创建端口样式的通用函数
 * @param portId 端口ID
 * @param alignment 端口位置
 * @param fromLinkable 是否可以作为连出端口
 * @param toLinkable 是否可以作为连入端口
 * @param fromMaxLinks 最大可连出线数量
 * @param toMaxLinks 最大可连入线数量
 * @returns GoJS Shape对象
 */
export const createPortStyle = (
	portId: string,
	alignment: go.Spot,
	fromLinkable: boolean = true,
	toLinkable: boolean = true,
	fromMaxLinks: number = Infinity,
	toMaxLinks: number = Infinity
) => {
	const $ = go.GraphObject.make;

	// 根据端口位置确定fromSpot和toSpot
	let fromSpot = alignment.equals(go.Spot.Top)
		? go.Spot.Top
		: alignment.equals(go.Spot.Bottom)
		? go.Spot.Bottom
		: alignment.equals(go.Spot.Left)
		? go.Spot.Left
		: alignment.equals(go.Spot.Right)
		? go.Spot.Right
		: go.Spot.AllSides;

	let toSpot = fromSpot;

	return $(
		go.Shape,
		'Circle', // 使用圆形端口
		{
			// === 关键优化：参考GoJS示例的端口设置 ===
			fill: 'black', // 半透明黑色，确保端口可见但不太突兀
			desiredSize: new go.Size(3, 3), // 增大到4x4，确保更好的交互性和可见性
			stroke: null, // 无边框
			alignment: alignment, // 端口位置
			alignmentFocus: go.Spot.Center, // 精确定位
			portId: portId, // 端口ID，这是关键！
			fromLinkable: fromLinkable, // 是否可以作为连出端口
			toLinkable: toLinkable, // 是否可以作为连入端口
			fromSpot: fromSpot, // 连线出发位置
			toSpot: toSpot, // 连线到达位置
			fromMaxLinks: fromMaxLinks, // 最大可连出线数量
			toMaxLinks: toMaxLinks, // 最大可连入线数量
			cursor: 'pointer', // 鼠标样式
			// === 简化的交互效果，参考示例 ===
			mouseEnter: (e: go.InputEvent, port: go.GraphObject) => {
				if (!e.diagram.isReadOnly) {
					(port as go.Shape).fill = 'rgba(0, 123, 255, 0.9)'; // 悬停时蓝色高亮
				}
			},
			mouseLeave: (e: go.InputEvent, port: go.GraphObject) => {
				// 恢复半透明状态，保持可见性
				(port as go.Shape).fill = 'black';
			},
		}
	);
};

/**
 * 创建通用节点文本标签
 * @param font 字体样式
 * @param alignment 对齐方式
 * @param margin 边距
 * @returns GoJS TextBlock对象
 */
export const createNodeLabel = ({
	font = '12px sans-serif',
	alignment = new go.Spot(0.5, 1, 0, 10),
	margin = new go.Margin(4, 0, 0, 0),
}: {
	font?: string;
	alignment?: go.Spot;
	margin?: go.Margin;
} = {}) => {
	const $ = go.GraphObject.make;

	return $(
		go.TextBlock,
		{
			margin: margin,
			font: font,
			wrap: go.TextBlock.WrapFit,
			editable: false,
			textAlign: 'center',
			maxSize: new go.Size(100, NaN),
			alignment: alignment,
		},
		new go.Binding('text', 'name').makeTwoWay()
	);
};

/**
 * 创建通用节点形状
 * @param geometryString 几何路径字符串
 * @param defaultColor 默认颜色
 * @param strokeWidth 线宽
 * @param width 宽度
 * @param height 高度
 * @returns GoJS Shape对象
 */
export const createNodeShape = (
	geometryString: string,
	defaultColor: string = '#1c57ea',
	strokeWidth: number = 1.5,
	width: number = 0,
	height: number = 0
) => {
	const $ = go.GraphObject.make;

	return $(
		go.Shape,
		{
			name: 'NODESHAPE',
			geometryString: geometryString,
			fill: 'transparent',
			stroke: defaultColor,
			strokeWidth: strokeWidth,
			width: width * 2,
			height: height * 2,
			alignment: go.Spot.Center,
		},
		new go.Binding('stroke', 'color')
	);
};

/**
 * 创建基础节点配置
 * @param locationSpot 位置锚点
 * @param selectionAdorned 是否显示选择装饰
 * @param resizable 是否可调整大小
 * @param rotatable 是否可旋转
 * @returns 基础节点配置对象
 */
export const createBaseNodeConfig = (
	locationSpot: go.Spot = go.Spot.Center,
	selectionAdorned: boolean = false,
	resizable: boolean = false,
	rotatable: boolean = true // 默认启用旋转功能，支持角度调整
) => {
	return {
		locationSpot: locationSpot,
		selectionAdorned: selectionAdorned,
		resizable: resizable,
		rotatable: rotatable,
		fromSpot: go.Spot.Center,
		toSpot: go.Spot.Center,
		fromLinkable: false, // 禁用直接从节点连线
		toLinkable: false, // 禁用直接到节点连线
		cursor: 'move',
		movable: true,
		dragComputation: function (thisPart: go.Part, newLoc: go.Point, snappedLoc: go.Point) {
			return snappedLoc; // 返回网格对齐的点
		},
	};
};

/**
 * 创建基础节点绑定
 * @returns 基础绑定数组
 */
export const createBaseNodeBindings = () => {
	const $ = go.GraphObject.make;

	return [
		new go.Binding('location', 'pos', (p) => {
			if (Array.isArray(p)) {
				return new go.Point(p[0], p[1]);
			}
			return new go.Point(0, 0);
		}).makeTwoWay((pt) => {
			return [pt.x, pt.y];
		}),
		// 添加角度绑定，支持节点旋转
		new go.Binding('angle', 'angle').makeTwoWay(),
	];
};

/**
 * 创建变压器线圈组件 - 每个线圈都是独立的圆圈，带有专用端口
 * @param coilId 线圈标识符 (如: 'H', 'M', 'L') 高压侧，中压侧，低压侧
 * @param position 线圈在节点中的相对位置坐标 (如: new go.Spot(0.5, 0) 表示上方中央)
 * @param portAlignment 连线端口的方向位置 (如: go.Spot.Top 表示连线从上方进出)
 * @param defaultColor 线圈的默认显示颜色 (如: '#1c57ea' 蓝色, '#ce4878' 粉色)
 * @param size 圆圈的直径尺寸，单位像素 (建议范围: 15-25)
 * @returns GoJS Panel对象，包含线圈圆圈和集成端口
 */
export const createTransformerCoil = (
	coilId: string,
	position: go.Spot,
	portAlignment: go.Spot,
	defaultColor: string = '#1c57ea',
	size: number = 20
) => {
	const $ = go.GraphObject.make;

	return $(
		go.Panel,
		'Spot', // 使用Spot面板布局，允许精确定位内部元素
		{
			alignment: position, // 设置线圈在主节点中的位置
			name: `COIL_${coilId}`, // 线圈面板的唯一名称，便于调试和引用
		},

		// === 线圈圆圈主体 ===
		$(
			go.Shape,
			'Circle', // 圆形形状
			{
				width: size, // 圆圈宽度
				height: size, // 圆圈高度
				fill: 'transparent', // 圆圈内部透明，只显示边框
				stroke: defaultColor, // 边框颜色，这是线圈的主要显示颜色
				strokeWidth: 1.5, // 边框线宽，影响线圈粗细显示
				name: `COIL_SHAPE_${coilId}`, // 形状的唯一名称，便于调试
			},
			new go.Binding('stroke', '', (data) => {
				switch (coilId) {
					case 'H':
						return data.color;
					case 'M':
						return data.color2;
					case 'L':
						return data.color3;
				}
			})
		),

		// === 集成端口 (参考GoJS示例的简化端口配置) ===
		$(
			go.Shape,
			'Circle', // 端口也是圆形
			{
				// --- 端口基础样式 (参考示例) ---
				fill: 'black', // 半透明黑色，确保端口可见但不太突兀
				desiredSize: new go.Size(3, 3), // 统一端口尺寸，确保良好交互性
				stroke: null, // 无边框

				// --- 端口位置设置 ---
				alignment: portAlignment, // 端口在圆圈中的实际位置
				alignmentFocus: go.Spot.Center, // 端口的锚点设置为中心

				// --- 连线配置 ---
				portId: coilId, // 端口唯一ID
				fromLinkable: true, // 允许从此端口连出线
				toLinkable: true, // 允许连线连入此端口
				fromSpot: portAlignment, // 连出线的起始方向
				toSpot: portAlignment, // 连入线的终止方向
				fromMaxLinks: Infinity, // 最大连出线数量
				toMaxLinks: Infinity, // 最大连入线数量
				cursor: 'pointer', // 鼠标悬停时显示手型光标

				// --- 简化的端口交互效果 (参考示例) ---
				mouseEnter: (e: go.InputEvent, port: go.GraphObject) => {
					if (!e.diagram.isReadOnly) {
						(port as go.Shape).fill = 'rgba(0, 123, 255, 0.9)'; // 悬停时蓝色高亮
					}
				},

				mouseLeave: (e: go.InputEvent, port: go.GraphObject) => {
					// 恢复半透明状态，保持可见性
					(port as go.Shape).fill = 'black';
				},
			}
		)
	);
};

/**
 * 创建主要内容面板
 * @param components 面板内容组件数组
 * @returns GoJS Panel对象
 */
export const createMainPanel = (...components: any[]) => {
	const $ = go.GraphObject.make;

	return $(
		go.Panel,
		'Spot',
		{
			alignment: go.Spot.Center,
			name: 'MAIN_PANEL',
		},
		...components
	);
};
