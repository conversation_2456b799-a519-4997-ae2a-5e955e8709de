<template>
	<div
		class="w-full h-full relative rounded-md"
		v-loading="isLoading || isLayouting"
		:element-loading-text="isLoading ? '加载厂站图中...' : '正在计算布局，请稍候...'"
	>
		<!-- 图表容器 -->
		<div ref="diagramRef" class="w-full h-full bg-white"></div>

		<!-- 工具栏 -->
		<div class="absolute top-0 left-0 right-0 z-10 bg-gray-50 p-2 border-b border-gray-200">
			<div class="flex items-center gap-2">
				<!-- 节点搜索组件 -->
				<NodeSearch :all-nodes="allNodes" :diagram="diagramInstance" @node-selected="handleSearchNodeSelected" />

				<!-- 工具按钮 -->
				<el-tooltip content="配置数据列" placement="top">
					<el-button class="!m-0" size="small" :icon="Setting" type="default" circle @click="openConfigDialog" />
				</el-tooltip>
				<el-tooltip content="放大" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomIn" type="default" circle @click="zoomIn" />
				</el-tooltip>
				<el-tooltip content="缩小" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomOut" type="default" circle @click="zoomOut" />
				</el-tooltip>
				<el-tooltip content="重置视图" placement="top">
					<el-button class="!m-0" size="small" :icon="FullScreen" type="default" circle @click="resetZoom" />
				</el-tooltip>
				<el-tooltip :content="showOverview ? '隐藏预览窗口' : '显示预览窗口'" placement="top">
					<el-button
						class="!m-0"
						:icon="showOverview ? Hide : View"
						size="small"
						:type="showOverview ? 'primary' : 'default'"
						circle
						@click="showOverview = !showOverview"
					/>
				</el-tooltip>
			</div>
		</div>

		<!-- 缩略图 -->
		<div
			v-show="showOverview"
			ref="overviewRef"
			class="absolute bottom-5 right-5 z-10 border border-gray-300 shadow-lg bg-white"
			style="width: 200px; height: 150px"
		/>

		<!-- 单线图弹窗 -->
		<el-dialog
			destroy-on-close
			class="!p-0"
			fullscreen
			body-class="!h-full !max-h-full"
			header-class="!p-0"
			v-model="isShowNodeInfo"
			width="90%"
			height="90%"
		>
			<SingleLineGraph v-if="nodeInfo" :node-info="nodeInfo" />
		</el-dialog>

		<!-- 配置数据列弹窗 -->
		<el-dialog v-model="showConfigDialog" title="配置数据列" width="600px" destroy-on-close>
			<div class="space-y-6">
				<!-- 线路指标配置 -->
				<div>
					<h4 class="text-base font-medium mb-3 flex items-center">
						<el-icon class="mr-2"><Setting /></el-icon>
						线路指标
					</h4>
					<div class="grid grid-cols-2 gap-4">
						<el-checkbox v-model="lineIndicatorConfig.showLineName">线路名称</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showP">P</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQ">P+jQ</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showLoading">loading%</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPM" disabled>P(M)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQM" disabled>P+jQ(M)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPDelta" disabled>P(△)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQDelta" disabled>P+jQ(△)</el-checkbox>
					</div>
					<p class="text-xs text-gray-500 mt-2">灰色选项表示数据暂未提供，后续将会支持</p>
				</div>
			</div>

			<template #footer>
				<div class="flex justify-end space-x-2">
					<el-button @click="showConfigDialog = false">取消</el-button>
					<el-button type="primary" @click="applyConfig">应用配置</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, shallowRef, watch, onMounted, inject, ComputedRef } from 'vue';
import * as go from 'gojs';
import { ElMessage } from 'element-plus';
import { View, Hide, ZoomIn, ZoomOut, FullScreen, Setting } from '@element-plus/icons-vue';
import SingleLineGraph from '../single-line/index.vue';
import NodeSearch from './components/NodeSearch.vue';
import { ParallelRouteLink } from './ParallelRouteLink';
import { query_substation_graph } from './api';
import { COLORS, ZOOM, getVoltageColor, MAX_PARALLEL_LINES, getLineSegmentFraction, getInnerCircles } from '/@/config/GraphConfig';

// ===== 组件默认参数 =====
const props = withDefaults(
	defineProps<{
		bb_diagram_layer_id: string;
	}>(),
	{
		bb_diagram_layer_id: '34',
	}
);

// ===== 自定义节点类 =====
class LabelAdornmentNode extends go.Node {
	static labelTemplate: go.Adornment | null = null;

	updateAdornments() {
		super.updateAdornments();
		if (LabelAdornmentNode.labelTemplate === null) return;

		let ad = this.findAdornment('LABEL');
		if (ad === null) {
			ad = LabelAdornmentNode.labelTemplate.copy();
			ad.adornedObject = this;
			this.addAdornment('LABEL', ad);
		}
	}
}

// ===== 数据注入 =====
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

// ===== 状态管理 =====
const isLoading = ref<boolean>(false);
const isLayouting = ref<boolean>(false);
const showOverview = ref<boolean>(false);
const showConfigDialog = ref<boolean>(false);

// ===== DOM引用 =====
const diagramRef = ref<HTMLElement | null>(null);
const overviewRef = ref<HTMLElement | null>(null);

// ===== GoJS实例 =====
let myDiagram: go.Diagram | null = null;
const diagramInstance = shallowRef<go.Diagram | null>(null);
let myOverview: go.Overview | null = null;
let $: any = null;

// ===== 节点数据状态 =====
const allNodes = ref<SubstationNode[]>([]);
const isShowNodeInfo = ref<boolean>(false);
const nodeInfo = ref<SubstationNode | null>(null);

// ===== 线路指标配置 =====
const lineIndicatorConfig = ref<LineIndicatorConfig>({
	showLineName: true,
	showP: false,
	showPQ: false,
	showLoading: true,
	showPM: false,
	showPQM: false,
	showPDelta: false,
	showPQDelta: false,
});

// ===== 数据转换函数 =====

/**
 * 确定节点类型
 */
const determineNodeType = (type: string): 'station' | 'plant' => {
	if (!type) return 'station';
	if (['变电站', '换流站', '开关站', '牵引站'].includes(type)) {
		return 'station';
	} else {
		return 'plant';
	}
};

/**
 * 将后端API数据转换为GoJS可用的节点数据
 */
const convertToNodeData = (apiData: any[]): SubstationNode[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	const result = apiData.map(
		(item): SubstationNode => ({
			key: item.id,
			name: item.name,
			type: item.type,
			category: determineNodeType(item.type),
			voltage: item.voltage || '',
			properties: item.properties,
			color: getVoltageColor(item.voltage) || COLORS.DEFAULT_LINE,
		})
	);

	allNodes.value = result;
	return result;
};

/**
 * 将后端API数据转换为GoJS可用的连接线数据
 */
const convertToLinkData = (apiData: any[]): SubstationLink[] => {
	if (!apiData || !Array.isArray(apiData)) return [];

	// 收集相同起点和终点的线路
	const routeGroups = new Map<string, any[]>();

	apiData.forEach((item) => {
		const fromId = item.source || '';
		const toId = item.target || '';
		const [sortedId1, sortedId2] = [fromId, toId].sort();
		const routeKey = `${sortedId1}-${sortedId2}`;

		if (!routeGroups.has(routeKey)) {
			routeGroups.set(routeKey, []);
		}
		routeGroups.get(routeKey)!.push(item);
	});

	// 为每组线路分配适当的lineCount和lineIndex
	const result: SubstationLink[] = [];

	routeGroups.forEach((group, routeKey) => {
		const lineCount = Math.min(group.length, MAX_PARALLEL_LINES);
		const [sortedId1, sortedId2] = routeKey.split('-');

		group.forEach((item, index) => {
			if (index >= MAX_PARALLEL_LINES) return;

			// 处理lineList字符串，转换为点数组
			let points: Point[] = [];
			if (item.lineList) {
				try {
					const lineListData = JSON.parse(item.lineList);
					if (Array.isArray(lineListData)) {
						points = lineListData.map((point) => ({
							x: parseFloat(point.x || 0),
							y: parseFloat(point.y || 0),
						}));
					}
				} catch (error) {
					console.error('解析lineList失败:', error);
				}
			}

			const fromId = item.source || '';
			const needSwap = fromId !== sortedId1;
			let direction = item.direction || 'forward';

			const linkData: SubstationLink = {
				name: item.name,
				from: sortedId1,
				to: sortedId2,
				source: item.source,
				target: item.target,
				color: getVoltageColor(item.voltage) || COLORS.DEFAULT_LINE,
				voltage: item.voltage,
				direction: direction,
				lineCount: lineCount,
				lineIndex: index,
				points: needSwap ? [...points].reverse() : points,
				properties: item.properties,
			};
			result.push(linkData);
		});
	});

	return result;
};

// ===== 格式化函数 =====
/**
 * 格式化复数显示
 */
const formatComplexNumber = (realPart: number, imagPart: number): string => {
	if (imagPart >= 0) {
		return `${realPart.toFixed(2)}+j${imagPart.toFixed(2)}`;
	} else {
		return `${realPart.toFixed(2)}-j${Math.abs(imagPart).toFixed(2)}`;
	}
};

/**
 * 格式化线路数据显示
 */
const formatLineDataSingle = (data: SubstationLink): string => {
	const config = lineIndicatorConfig.value;
	const props: any = data.properties || {};

	// From端数据
	const fromParts: string[] = [];
	if (config.showP) {
		const pFromValue = props.p_from_mw;
		if (pFromValue !== undefined && pFromValue !== null) {
			fromParts.push(`${pFromValue.toFixed(2)}MW`);
		}
	}
	if (config.showPQ) {
		const pFromValue = props.p_from_mw;
		const qFromValue = props.q_from_mvar;
		if (pFromValue !== undefined && pFromValue !== null && qFromValue !== undefined && qFromValue !== null) {
			fromParts.push(formatComplexNumber(pFromValue, qFromValue) + 'MW');
		}
	}

	// To端数据
	const toParts: string[] = [];
	if (config.showP) {
		const pToValue = props.p_to_mw;
		if (pToValue !== undefined && pToValue !== null) {
			toParts.push(`${pToValue.toFixed(2)}MW`);
		}
	}
	if (config.showPQ) {
		const pToValue = props.p_to_mw;
		const qToValue = props.q_to_mva;
		if (pToValue !== undefined && pToValue !== null && qToValue !== undefined && qToValue !== null) {
			toParts.push(formatComplexNumber(pToValue, qToValue) + 'MW');
		}
	}

	// 中间数据
	const middleParts: string[] = [];
	if (config.showLineName) {
		middleParts.push(data.name || '');
	}
	if (config.showLoading) {
		if (props.loading_percent !== undefined && props.loading_percent !== null) {
			middleParts.push(`${Number(props.loading_percent).toFixed(1)}%`);
		}
	}

	const fromText = fromParts.join(' ');
	const middleText = middleParts.join(' ');
	const toText = toParts.join(' ');

	const displayParts = [fromText, middleText, toText].filter((part) => part.trim().length > 0);
	return displayParts.join('  ');
};

// ===== 模板设置 =====

/**
 * 设置节点模板
 */
const setupNodeTemplates = () => {
	if (!myDiagram || !$) return;

	// 变电站节点模板
	const stationTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'MAIN_PANEL',
			cursor: 'pointer',
			selectionAdorned: false,
			selectionObjectName: 'MAIN_PANEL',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				{
					locationSpot: go.Spot.Center,
				},
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE,
					strokeWidth: 3,
					strokeDashArray: [10, 5],
					width: 26 * ZOOM + 6,
					height: 26 * ZOOM + 6,
				})
			),
		},
		new go.Binding('click', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					nodeInfo.value = nodeData;
					!dataPacket.value?.isHideNodeInfo && (isShowNodeInfo.value = true);
				}
			};
		}),
		new go.Binding('location', '', (data: SubstationNode) => {
			if (data.x !== undefined && data.y !== undefined) {
				return new go.Point(data.x, data.y);
			}
			return undefined;
		}),
		$(
			go.Panel,
			'Spot',
			{
				name: 'MAIN_PANEL',
				isPanelMain: true,
				itemTemplate: $(
					go.Panel,
					'Spot',
					$(
						go.Shape,
						'Circle',
						{
							fill: COLORS.WHITE,
							strokeWidth: 1.5,
						},
						new go.Binding('portId', '', (data, obj) => {
							const panel = obj.panel;
							const index = panel?.itemIndex ?? 0;
							const isOuter = data.isOuter || false;
							return isOuter ? '' : null;
						}),
						new go.Binding('width', 'diameter'),
						new go.Binding('height', 'diameter'),
						new go.Binding('stroke'),
						new go.Binding('strokeWidth')
					)
				),
			},
			new go.Binding('itemArray', '', (data) => getInnerCircles(data.voltage || '')),
			$(
				go.Panel,
				'Vertical',
				{
					alignment: go.Spot.Top,
					alignmentFocus: go.Spot.Bottom,
					isPanelMain: false,
					pickable: false,
				},
				new go.Binding('margin', '', (data: SubstationNode) => {
					const scaledRadius = (26 * ZOOM) / 2;
					const upwardOffset = -(scaledRadius + 15);
					return new go.Margin(0, 2, upwardOffset * 3.6, 2);
				}),
				// 节点名称
				$(
					go.TextBlock,
					{
						font: 'bold 10px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(2, 4, 2, 4),
						background: 'rgba(255, 255, 255, 0.9)',
						editable: false,
					},
					new go.Binding('text', 'name'),
					new go.Binding('stroke', 'color')
				),
				// U值显示
				$(
					go.TextBlock,
					{
						font: '8px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(1, 4, 1, 4),
						background: 'rgba(255, 255, 255, 0.8)',
						editable: false,
					},
					new go.Binding('text', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return props['U'] || '';
					}),
					new go.Binding('visible', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return !!(props['U'] || '').trim();
					}),
					new go.Binding('stroke', 'color')
				),
				// U(M)值显示
				$(
					go.TextBlock,
					{
						font: '8px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(1, 4, 1, 4),
						background: 'rgba(255, 255, 255, 0.8)',
						editable: false,
					},
					new go.Binding('text', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return props['U(M)'] || '';
					}),
					new go.Binding('visible', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return !!(props['U(M)'] || '').trim();
					}),
					new go.Binding('stroke', 'color')
				)
			)
		)
	);

	// 发电厂模板
	const plantTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'OUTER_SHAPE',
			cursor: 'pointer',
			selectionAdorned: false,
			selectionObjectName: 'OUTER_SHAPE',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				{
					locationSpot: go.Spot.Center,
				},
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE,
					strokeWidth: 3,
					strokeDashArray: [10, 5],
					width: 26 * ZOOM + 6,
					height: 26 * ZOOM + 6,
				})
			),
		},
		new go.Binding('location', '', (data: SubstationNode) => {
			if (data.x !== undefined && data.y !== undefined) {
				return new go.Point(data.x, data.y);
			}
			return undefined;
		}),
		new go.Binding('click', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					nodeInfo.value = nodeData;
					!dataPacket.value?.isHideNodeInfo && (isShowNodeInfo.value = true);
				}
			};
		}),
		$(go.Shape, 'Circle', {
			name: 'OUTER_SHAPE',
			width: 26 * ZOOM,
			height: 26 * ZOOM,
			fill: COLORS.WHITE,
			strokeWidth: 1.5,
			stroke: COLORS.BLACK,
			portId: '',
		}),
		$(go.Shape, {
			geometryString: 'M -15 0 C -5 -10 5 10 15 0',
			stroke: COLORS.BLACK,
			strokeWidth: 1.5,
			width: 30,
			height: 15,
			fill: null,
		}),
		$(
			go.Panel,
			'Vertical',
			{
				alignment: go.Spot.Top,
				alignmentFocus: go.Spot.Bottom,
				margin: new go.Margin(0, 2, -15, 2),
				isPanelMain: false,
				pickable: false,
			},
			// 节点名称
			$(
				go.TextBlock,
				{
					font: 'bold 10px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(2, 4, 2, 4),
					background: 'rgba(255, 255, 255, 0.9)',
					editable: false,
				},
				new go.Binding('text', 'name')
			),
			// U值显示
			$(
				go.TextBlock,
				{
					font: '8px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(1, 4, 1, 4),
					background: 'rgba(255, 255, 255, 0.8)',
					editable: false,
				},
				new go.Binding('text', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return props['U'] || '';
				}),
				new go.Binding('visible', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return !!(props['U'] || '').trim();
				})
			),
			// U(M)值显示
			$(
				go.TextBlock,
				{
					font: '8px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(1, 4, 1, 4),
					background: 'rgba(255, 255, 255, 0.8)',
					editable: false,
				},
				new go.Binding('text', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return props['U(M)'] || '';
				}),
				new go.Binding('visible', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return !!(props['U(M)'] || '').trim();
				})
			)
		)
	);

	const templmap = new go.Map<string, go.Part>();
	templmap.add('station', stationTemplate);
	templmap.add('plant', plantTemplate);
	myDiagram.nodeTemplateMap = templmap;
};

/**
 * 设置连接线模板
 */
const setupLinkTemplate = () => {
	if (!myDiagram || !$) return;

	myDiagram.linkTemplate = new ParallelRouteLink({
		relinkableFrom: true,
		relinkableTo: true,
		reshapable: true,
		curve: go.Link.JumpOver,
		layerName: 'Background',
	})
		.add(
			new go.Shape({
				strokeWidth: 1,
				stroke: COLORS.KV_500,
				name: 'SHAPE',
				strokeDashArray: [0],
			})
				.bind('stroke', 'color')
				.bind('strokeDashArray', '', () => [0])
		)
		.add(
			new go.Shape({
				toArrow: 'OpenTriangle',
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5,
				segmentIndex: 0,
				segmentFraction: 0.5,
				segmentOrientation: go.Link.OrientAlong,
			})
				.bind('stroke', 'color')
				.bind('fill', 'color')
				.bind('scale', '', () => 1.5)
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('visible', '', (data: SubstationLink) => {
					const sourceId = data?.source || '';
					const direction = data?.direction || 'forward';
					const showForwardArrow = (data.from === sourceId && direction === 'forward') || (data.from !== sourceId && direction === 'backward');
					return showForwardArrow;
				})
		)
		.add(
			new go.Shape({
				fromArrow: 'BackwardOpenTriangle',
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5,
				segmentIndex: 0,
				segmentFraction: 0.5,
				segmentOrientation: go.Link.OrientAlong,
			})
				.bind('stroke', 'color')
				.bind('fill', 'color')
				.bind('scale', '', () => 1.5)
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('visible', '', (data: SubstationLink) => {
					const sourceId = data?.source || '';
					const direction = data?.direction || 'forward';
					const showBackwardArrow = (data.from === sourceId && direction === 'backward') || (data.from !== sourceId && direction === 'forward');
					return showBackwardArrow;
				})
		)
		.add(
			new go.TextBlock({
				segmentOffset: new go.Point(10, -10),
				font: '9px 宋体',
				stroke: COLORS.DEFAULT,
				background: 'rgba(255, 255, 255, 0.9)',
				segmentOrientation: go.Link.OrientUpright,
			})
				.bind('text', '', (data: SubstationLink) => formatLineDataSingle(data))
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('stroke', '', (data: SubstationLink) => data.color)
		);
};

/**
 * 设置图表布局
 */
const setupLayout = () => {
	if (!myDiagram) return;

	const layout = myDiagram.layout as go.ForceDirectedLayout;
	layout.defaultSpringLength = 250;
	layout.defaultElectricalCharge = 350;
	layout.maxIterations = 300;

	myDiagram.layout.isInitial = true;
	myDiagram.layout.isOngoing = true;
};

// ===== 图表初始化 =====

/**
 * 初始化图表
 */
const initDiagram = () => {
	if (!diagramRef.value) return;

	$ = go.GraphObject.make;

	myDiagram = $(go.Diagram, diagramRef.value, {
		initialContentAlignment: go.Spot.Center,
		'undoManager.isEnabled': true,
		'clickCreatingTool.isEnabled': false,
		'draggingTool.isGridSnapEnabled': true,
		layout: $(go.ForceDirectedLayout, {
			maxIterations: 200,
			defaultSpringLength: 150,
			defaultElectricalCharge: 200,
		}),
	});

	setupNodeTemplates();
	setupLinkTemplate();
	setupLayout();
	loadGraphData();

	diagramInstance.value = myDiagram;
};

/**
 * 初始化缩略图
 */
const initOverview = () => {
	if (!overviewRef.value || !myDiagram || !$) return;

	myOverview = $(go.Overview, overviewRef.value, {
		observed: myDiagram,
		contentAlignment: go.Spot.Center,
		box: $(
			go.Part,
			$(go.Shape, {
				fill: 'rgba(100, 149, 237, 0.2)',
				stroke: 'cornflowerblue',
				strokeWidth: 2,
			})
		),
	});
};

/**
 * 加载图表数据
 */
const loadGraphData = async (nodeData?: SubstationNode[], linkData?: SubstationLink[]) => {
	if (!myDiagram) return;

	isLoading.value = true;

	try {
		const { data } = await query_substation_graph({
			bb_case_id: dataPacket.value.id,
			bb_diagram_layer_id: '34',
		});

		const apiNodes = data.nodes || [];
		const apiLinks = data.edges || [];

		nodeData = convertToNodeData(apiNodes);
		linkData = convertToLinkData(apiLinks);
	} catch (error) {
		console.error('加载API数据失败:', error);
		ElMessage.error('加载API数据失败');
		return;
	} finally {
		isLoading.value = false;
	}

	if (!nodeData || !linkData) return;

	isLayouting.value = true;

	myDiagram.model = new go.GraphLinksModel(nodeData, linkData);
	myDiagram.layoutDiagram(true);

	setTimeout(() => {
		if (myDiagram) {
			myDiagram.scale = 1;
			setTimeout(() => {
				if (myDiagram) {
					const docBounds = myDiagram.documentBounds;
					if (docBounds.width > 0 && docBounds.height > 0) {
						myDiagram.centerRect(docBounds);
					} else {
						myDiagram.scrollToRect(docBounds);
					}
				}
				isLayouting.value = false;
			}, 100);
		}
	}, 1000);
};

// ===== 事件处理 =====

/**
 * 处理搜索组件的节点选择事件
 */
const handleSearchNodeSelected = (nodeData: SubstationNode) => {
	nodeInfo.value = nodeData;
	emit('node-selected', nodeData);
};

// ===== 视图控制 =====

const zoomIn = () => {
	if (!myDiagram) return;
	myDiagram.commandHandler.increaseZoom();
};

const zoomOut = () => {
	if (!myDiagram) return;
	myDiagram.commandHandler.decreaseZoom();
};

const resetZoom = () => {
	if (!myDiagram) return;
	myDiagram.zoomToFit();
	myDiagram.contentAlignment = go.Spot.Center;
};

// ===== 配置管理 =====

const openConfigDialog = () => {
	showConfigDialog.value = true;
};

const applyConfig = () => {
	showConfigDialog.value = false;
	if (!myDiagram) return;

	myDiagram.links.each((link) => {
		link.updateTargetBindings();
	});
};

// ===== 组件事件 =====
const emit = defineEmits(['node-selected']);

// ===== 监听器设置 =====

watch(showOverview, (newValue) => {
	if (newValue) {
		setTimeout(() => {
			initOverview();
		}, 100);
	}
});

watch(
	lineIndicatorConfig,
	() => {
		if (myDiagram) {
			myDiagram.requestUpdate();
		}
	},
	{ deep: true }
);

watch(
	() => nodeInfo.value,
	(newVal) => {
		emit('node-selected', newVal);
	}
);

// ===== 组件初始化 =====
onMounted(() => {
	initDiagram();
});

// ===== 组件接口 =====
defineExpose({
	diagram: diagramInstance,
});
</script>
