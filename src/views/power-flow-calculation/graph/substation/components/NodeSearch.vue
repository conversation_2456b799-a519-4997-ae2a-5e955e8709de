<template>
	<el-select
		v-model="selectedNode"
		filterable
		remote
		reserve-keyword
		placeholder="输入关键词搜索节点"
		:remote-method="handleRemoteSearch"
		clearable
		class="!w-80"
		size="small"
		@change="handleNodeSelect"
	>
		<el-option v-for="item in filteredOptions" :key="item.key" :label="item.label" :value="item.key">
			<span>{{ item.label }}</span>
			<span class="text-xs text-gray-500 ml-2">({{ item.type === 'plant' ? '发电厂' : '变电站' }})</span>
		</el-option>
	</el-select>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import * as go from 'gojs';

// Props定义
const props = defineProps<{
	allNodes: SubstationNode[];
	diagram: go.Diagram | null;
}>();

// Events定义
const emit = defineEmits<{
	(e: 'node-selected', nodeData: SubstationNode): void;
}>();

// 搜索相关状态
const selectedNode = ref<string>('');
const searchQuery = ref<string>('');

// ===== 计算属性 =====

/**
 * 基础节点选项（所有节点转换为选项格式）
 */
const baseNodeOptions = computed<NodeSearchOption[]>(() => {
	return props.allNodes.map((node) => ({
		key: node.key,
		label: node.name,
		type: node.type,
	}));
});

/**
 * 过滤后的选项（基于搜索条件过滤）
 */
const filteredOptions = computed<NodeSearchOption[]>(() => {
	if (!searchQuery.value) {
		// 没有搜索条件时，返回前50个选项
		return baseNodeOptions.value.slice(0, 50);
	}

	// 有搜索条件时，进行过滤
	const filtered = baseNodeOptions.value.filter((option) => {
		const node = props.allNodes.find((n) => n.key === option.key);
		return node ? matchesSearchQuery(node, searchQuery.value) : false;
	});

	return filtered;
});

// ===== 拼音搜索工具函数 =====

/**
 * 获取汉字的拼音首字母
 * 简化版拼音匹配，仅支持常用汉字
 */
const getPinyinInitials = (str: string): string => {
	if (!str) return '';

	// 常用汉字拼音首字母映射表（简化版）
	const pinyinMap: { [key: string]: string } = {
		变: 'b',
		电: 'd',
		站: 'z',
		厂: 'c',
		能: 'n',
		源: 'y',
		火: 'h',
		风: 'f',
		太: 't',
		阳: 'y',
		水: 's',
		核: 'h',
		发: 'f',
		城: 'c',
		区: 'q',
		南: 'n',
		北: 'b',
		东: 'd',
		西: 'x',
		中: 'z',
		新: 'x',
		河: 'h',
		湖: 'h',
		山: 's',
		海: 'h',
		洋: 'y',
		京: 'j',
		津: 'j',
		冀: 'j',
		晋: 'j',
		蒙: 'm',
		辽: 'l',
		吉: 'j',
		黑: 'h',
		沪: 'h',
		苏: 's',
		浙: 'z',
		皖: 'w',
		闽: 'm',
		赣: 'g',
		鲁: 'l',
		豫: 'y',
		鄂: 'e',
		湘: 'x',
		粤: 'y',
		桂: 'g',
		琼: 'q',
		渝: 'y',
		川: 'c',
		贵: 'g',
		云: 'y',
		藏: 'z',
		陕: 's',
		甘: 'g',
		青: 'q',
		宁: 'n',
		新疆: 'xj',
	};

	let result = '';
	for (let i = 0; i < str.length; i++) {
		const char = str[i];
		if (/[\u4e00-\u9fa5]/.test(char)) {
			if (pinyinMap[char]) {
				result += pinyinMap[char];
			}
		} else {
			result += char.toLowerCase();
		}
	}
	return result;
};

/**
 * 匹配节点是否符合搜索条件
 * 匹配名称、类型、区域等多个属性，支持拼音首字母匹配
 */
const matchesSearchQuery = (nodeData: SubstationNode, searchQueryValue: string): boolean => {
	if (!nodeData || !searchQueryValue) return false;

	const query = searchQueryValue.toLowerCase();

	// 匹配名称
	if (nodeData.name && nodeData.name.toLowerCase().includes(query)) {
		return true;
	}

	// 匹配拼音首字母
	if (nodeData.name) {
		const pinyinInitials = getPinyinInitials(nodeData.name);
		if (pinyinInitials.includes(query)) {
			return true;
		}
	}

	// 匹配类型和电压等级
	if (nodeData.type && nodeData.type.toLowerCase().includes(query)) {
		return true;
	}
	if (nodeData.voltage && nodeData.voltage.toLowerCase().includes(query)) {
		return true;
	}

	return false;
};

// ===== 搜索功能 =====

/**
 * 处理远程搜索
 * 更新搜索条件，触发计算属性重新计算
 */
const handleRemoteSearch = (query: string) => {
	searchQuery.value = query;
};

/**
 * 为节点添加震动效果，吸引用户注意
 */
const shakeNode = (node: any) => {
	if (!node || !props.diagram) return;

	const origLoc = node.location.copy();
	let count = 0;

	const interval = setInterval(() => {
		if (count >= 8) {
			clearInterval(interval);
			node.location = origLoc;
			return;
		}

		const offsetX = (Math.random() - 0.5) * 5;
		const offsetY = (Math.random() - 0.5) * 5;
		node.location = new go.Point(origLoc.x + offsetX, origLoc.y + offsetY);
		count++;
	}, 50);
};

/**
 * 处理节点选择事件
 * 当下拉框选中某个节点时，将视图中心移动到该节点并高亮显示
 */
const handleNodeSelect = (key: string) => {
	if (!key || !props.diagram) return;

	const node = props.diagram.findNodeForKey(key);
	if (node) {
		props.diagram.clearSelection();
		props.diagram.select(node);
		props.diagram.centerRect(node.actualBounds);
		shakeNode(node);

		const nodeData = node.data as SubstationNode;
		console.log(`显示节点: ${nodeData.name}`);

		// 抛出选中事件
		emit('node-selected', nodeData);
	}
};

// 暴露方法供父组件调用
defineExpose({
	clearSelection: () => {
		selectedNode.value = '';
		searchQuery.value = '';
	},
	searchNodes: handleRemoteSearch,
});
</script>
