<!DOCTYPE html>
<html>
<head>
    <title>GoJS 3.0.9 - 差异化右键菜单演示</title>
    <script src="https://cdn.jsdelivr.net/npm/gojs@3.0.9/release/go.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        #myDiagramDiv {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* 节点右键菜单样式 */
        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 6px 0;
            font-family: Arial, sans-serif;
            font-size: 13px;
            z-index: 1000;
            display: none;
            min-width: 160px;
        }
        
        .context-menu.show-menu {
            display: block;
        }
        
        .menu-item {
            padding: 8px 16px;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            color: #333;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }
        
        .menu-item:hover:not(:disabled) {
            background-color: #e3f2fd;
        }
        
        .menu-item:disabled {
            color: #999;
            cursor: not-allowed;
        }
        
        .menu-item .icon {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }
        
        .menu-separator {
            height: 1px;
            background-color: #eee;
            margin: 4px 0;
        }
        
        .menu-title {
            padding: 6px 16px;
            font-weight: bold;
            color: #666;
            font-size: 11px;
            text-transform: uppercase;
            background-color: #f8f9fa;
        }
        
        /* 不同菜单的特殊样式 */
        #nodeContextMenu .menu-title {
            color: #1976d2;
        }
        
        #linkContextMenu .menu-title {
            color: #388e3c;
        }
        
        #diagramContextMenu .menu-title {
            color: #f57c00;
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            background: #2196f3;
            color: white;
            transition: all 0.3s;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .info-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            max-width: 300px;
            font-size: 12px;
            display: none;
        }
        
        .info-panel.show {
            display: block;
        }
    </style>
</head>
<body>
    <h1>GoJS 差异化右键菜单演示</h1>
    
    <div class="controls">
        <button onclick="clearHighlights()">清除高亮</button>
        <button onclick="showAllMenus()">显示所有菜单类型</button>
        <button onclick="resetDiagram()">重置图表</button>
    </div>
    
    <div id="myDiagramDiv"></div>
    
    <!-- 节点右键菜单 -->
    <div id="nodeContextMenu" class="context-menu">
        <div class="menu-title">节点操作</div>
        <button class="menu-item" onclick="nodeAction('info')">
            <span class="icon">ℹ️</span>节点详情
        </button>
        <button class="menu-item" onclick="nodeAction('connections')">
            <span class="icon">🔗</span>显示连接
        </button>
        <button class="menu-item" onclick="nodeAction('isolate')">
            <span class="icon">🔒</span>隔离节点
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="nodeAction('edit')">
            <span class="icon">✏️</span>编辑属性
        </button>
        <button class="menu-item" onclick="nodeAction('copy')">
            <span class="icon">📋</span>复制节点
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="nodeAction('delete')" style="color: #d32f2f;">
            <span class="icon">🗑️</span>删除节点
        </button>
    </div>
    
    <!-- 连线右键菜单 -->
    <div id="linkContextMenu" class="context-menu">
        <div class="menu-title">线路操作</div>
        <button class="menu-item" onclick="linkAction('info')">
            <span class="icon">📊</span>线路信息
        </button>
        <button class="menu-item" onclick="linkAction('highlight')">
            <span class="icon">🎯</span>高亮路径
        </button>
        <button class="menu-item" onclick="linkAction('reverse')">
            <span class="icon">🔄</span>反向连接
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="linkAction('properties')">
            <span class="icon">⚙️</span>线路参数
        </button>
        <button class="menu-item" onclick="linkAction('split')">
            <span class="icon">✂️</span>分割线路
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="linkAction('delete')" style="color: #d32f2f;">
            <span class="icon">❌</span>删除线路
        </button>
    </div>
    
    <!-- 图表背景右键菜单 -->
    <div id="diagramContextMenu" class="context-menu">
        <div class="menu-title">图表操作</div>
        <button class="menu-item" onclick="diagramAction('addNode')">
            <span class="icon">➕</span>添加节点
        </button>
        <button class="menu-item" onclick="diagramAction('paste')">
            <span class="icon">📄</span>粘贴
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="diagramAction('selectAll')">
            <span class="icon">🔘</span>全选
        </button>
        <button class="menu-item" onclick="diagramAction('layout')">
            <span class="icon">📐</span>重新布局
        </button>
        <div class="menu-separator"></div>
        <button class="menu-item" onclick="diagramAction('export')">
            <span class="icon">💾</span>导出图片
        </button>
    </div>
    
    <!-- 信息面板 -->
    <div id="infoPanel" class="info-panel">
        <div id="infoPanelContent"></div>
    </div>

    <script>
        let myDiagram = null;
        let currentContextObject = null; // 当前右键点击的对象
        
        function init() {
            // 创建图表实例
            myDiagram = new go.Diagram("myDiagramDiv", {
                "undoManager.isEnabled": true,
                layout: new go.ForceDirectedLayout({
                    maxIterations: 200,
                    defaultSpringLength: 100,
                    defaultElectricalCharge: 150
                }),
                initialScale: 0.8
            });

            // 创建三个不同的右键菜单
            const nodeContextMenu = createContextMenu("nodeContextMenu");
            const linkContextMenu = createContextMenu("linkContextMenu");
            const diagramContextMenu = createContextMenu("diagramContextMenu");

            // 节点模板 - 绑定节点专用菜单
            myDiagram.nodeTemplate = 
                new go.Node("Auto", {
                    contextMenu: nodeContextMenu
                })
                .add(
                    new go.Shape("RoundedRectangle", {
                        strokeWidth: 2,
                        portId: "",
                        cursor: "pointer",
                        fromLinkable: true,
                        toLinkable: true
                    })
                    .bind("fill", "type", function(type) {
                        if (type === "变电站") return "#e3f2fd";
                        if (type === "电厂") return "#fff3e0";
                        return "#f3e5f5";
                    })
                    .bind("stroke", "type", function(type) {
                        if (type === "变电站") return "#1976d2";
                        if (type === "电厂") return "#f57c00";
                        return "#7b1fa2";
                    })
                    .bind("strokeWidth", "isHighlighted", function(h) {
                        return h ? 4 : 2;
                    }),
                    
                    new go.TextBlock({
                        margin: 10,
                        font: "bold 12px sans-serif",
                        textAlign: "center"
                    })
                    .bind("text", "name")
                );

            // 连线模板 - 绑定连线专用菜单
            myDiagram.linkTemplate = 
                new go.Link({
                    routing: go.Link.AvoidsNodes,
                    curve: go.Link.JumpOver,
                    contextMenu: linkContextMenu
                })
                .add(
                    new go.Shape({
                        strokeWidth: 2,
                        stroke: "#666"
                    })
                    .bind("stroke", "isHighlighted", function(h) {
                        return h ? "#ff4444" : "#666";
                    })
                    .bind("strokeWidth", "isHighlighted", function(h) {
                        return h ? 4 : 2;
                    }),
                    
                    new go.Shape({
                        toArrow: "Standard",
                        stroke: "#666",
                        fill: "#666"
                    })
                    .bind("stroke", "isHighlighted", function(h) {
                        return h ? "#ff4444" : "#666";
                    })
                    .bind("fill", "isHighlighted", function(h) {
                        return h ? "#ff4444" : "#666";
                    }),
                    
                    // 连线标签
                    new go.TextBlock({
                        segmentIndex: 0,
                        segmentFraction: 0.5,
                        font: "10px sans-serif",
                        background: "white",
                        stroke: "#666"
                    })
                    .bind("text", "label")
                );

            // 图表背景右键菜单
            myDiagram.contextMenu = diagramContextMenu;

            // 简化的模拟数据
            const nodeDataArray = [
                { key: "A", name: "变电站A", type: "变电站", voltage: "500kV" },
                { key: "B", name: "变电站B", type: "变电站", voltage: "220kV" },
                { key: "C", name: "电厂C", type: "电厂", capacity: "1000MW" },
                { key: "D", name: "变电站D", type: "变电站", voltage: "110kV" },
                { key: "E", name: "电厂E", type: "电厂", capacity: "500MW" }
            ];

            const linkDataArray = [
                { from: "A", to: "B", label: "线路1", voltage: "220kV", length: "50km" },
                { from: "A", to: "C", label: "线路2", voltage: "500kV", length: "80km" },
                { from: "B", to: "D", label: "线路3", voltage: "110kV", length: "30km" },
                { from: "C", to: "E", label: "线路4", voltage: "220kV", length: "60km" },
                { from: "D", to: "E", label: "线路5", voltage: "110kV", length: "40km" }
            ];

            myDiagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);
        }

        // 创建右键菜单的通用函数
        function createContextMenu(menuId) {
            return new go.HTMLInfo({
                show: function(obj, diagram, tool) {
                    showContextMenu(obj, diagram, tool, menuId);
                },
                hide: function() {
                    hideContextMenu(menuId);
                }
            });
        }

        // 显示右键菜单
        function showContextMenu(obj, diagram, tool, menuId) {
            const menuElement = document.getElementById(menuId);
            currentContextObject = obj; // 保存当前操作的对象
            
            // 隐藏其他菜单
            hideAllMenus();
            
            // 显示当前菜单
            menuElement.classList.add("show-menu");
            const mousePt = diagram.lastInput.viewPoint;
            menuElement.style.left = mousePt.x + 5 + "px";
            menuElement.style.top = mousePt.y + "px";
            
            // 根据对象类型更新菜单项状态
            updateMenuItems(obj, menuId);
            
            // 添加全局点击监听器
            setTimeout(() => {
                document.addEventListener("click", function hideOnClick() {
                    hideContextMenu(menuId);
                    document.removeEventListener("click", hideOnClick);
                }, true);
            }, 0);
        }

        // 隐藏指定菜单
        function hideContextMenu(menuId) {
            const menuElement = document.getElementById(menuId);
            menuElement.classList.remove("show-menu");
        }

        // 隐藏所有菜单
        function hideAllMenus() {
            document.querySelectorAll('.context-menu').forEach(menu => {
                menu.classList.remove("show-menu");
            });
        }

        // 根据对象类型更新菜单项状态
        function updateMenuItems(obj, menuId) {
            const menuItems = document.querySelectorAll(`#${menuId} .menu-item`);
            
            // 这里可以根据对象的具体属性来启用/禁用菜单项
            menuItems.forEach(item => {
                item.disabled = false; // 默认启用所有项
            });
            
            // 示例：如果是特定类型的节点，禁用某些操作
            if (obj && obj instanceof go.Node && obj.data.type === "电厂") {
                // 电厂节点可能不允许某些操作
                // 这里可以添加具体的逻辑
            }
        }

        // 节点操作函数
        function nodeAction(action) {
            if (!currentContextObject || !(currentContextObject instanceof go.Node)) return;
            
            const node = currentContextObject;
            const data = node.data;
            
            myDiagram.startTransaction(action);
            
            switch(action) {
                case 'info':
                    showInfo(`节点信息: ${data.name}\n类型: ${data.type}\n电压: ${data.voltage || 'N/A'}\n容量: ${data.capacity || 'N/A'}`);
                    break;
                case 'connections':
                    highlightNodeConnections(node);
                    break;
                case 'isolate':
                    isolateNode(node);
                    break;
                case 'edit':
                    editNodeProperties(node);
                    break;
                case 'copy':
                    copyNode(node);
                    break;
                case 'delete':
                    myDiagram.model.removeNodeData(data);
                    break;
            }
            
            myDiagram.commitTransaction(action);
            hideAllMenus();
        }

        // 连线操作函数
        function linkAction(action) {
            if (!currentContextObject || !(currentContextObject instanceof go.Link)) return;
            
            const link = currentContextObject;
            const data = link.data;
            
            myDiagram.startTransaction(action);
            
            switch(action) {
                case 'info':
                    showInfo(`线路信息: ${data.label}\n电压等级: ${data.voltage}\n长度: ${data.length}\n起点: ${data.from}\n终点: ${data.to}`);
                    break;
                case 'highlight':
                    highlightLink(link);
                    break;
                case 'reverse':
                    reverseLink(link);
                    break;
                case 'properties':
                    editLinkProperties(link);
                    break;
                case 'split':
                    splitLink(link);
                    break;
                case 'delete':
                    myDiagram.model.removeLinkData(data);
                    break;
            }
            
            myDiagram.commitTransaction(action);
            hideAllMenus();
        }

        // 图表操作函数
        function diagramAction(action) {
            myDiagram.startTransaction(action);
            
            switch(action) {
                case 'addNode':
                    addNewNode();
                    break;
                case 'paste':
                    myDiagram.commandHandler.pasteSelection();
                    break;
                case 'selectAll':
                    myDiagram.commandHandler.selectAll();
                    break;
                case 'layout':
                    myDiagram.layoutDiagram(true);
                    break;
                case 'export':
                    exportDiagram();
                    break;
            }
            
            myDiagram.commitTransaction(action);
            hideAllMenus();
        }

        // 辅助函数
        function showInfo(message) {
            const panel = document.getElementById('infoPanel');
            const content = document.getElementById('infoPanelContent');
            content.innerHTML = message.replace(/\n/g, '<br>');
            panel.classList.add('show');
            
            setTimeout(() => {
                panel.classList.remove('show');
            }, 3000);
        }

        function highlightNodeConnections(node) {
            clearHighlights();
            myDiagram.model.setDataProperty(node.data, "isHighlighted", true);
            node.findLinksConnected().each(function(link) {
                myDiagram.model.setDataProperty(link.data, "isHighlighted", true);
                const otherNode = link.getOtherNode(node);
                if (otherNode) {
                    myDiagram.model.setDataProperty(otherNode.data, "isHighlighted", true);
                }
            });
        }

        function highlightLink(link) {
            clearHighlights();
            myDiagram.model.setDataProperty(link.data, "isHighlighted", true);
            myDiagram.model.setDataProperty(link.fromNode.data, "isHighlighted", true);
            myDiagram.model.setDataProperty(link.toNode.data, "isHighlighted", true);
        }

        function isolateNode(node) {
            const linksToRemove = [];
            node.findLinksConnected().each(function(link) {
                linksToRemove.push(link.data);
            });
            linksToRemove.forEach(function(linkData) {
                myDiagram.model.removeLinkData(linkData);
            });
            showInfo(`节点 ${node.data.name} 已被隔离`);
        }

        function reverseLink(link) {
            const data = link.data;
            myDiagram.model.setDataProperty(data, "from", data.to);
            myDiagram.model.setDataProperty(data, "to", data.from);
            showInfo(`线路 ${data.label} 方向已反转`);
        }

        function editNodeProperties(node) {
            const newName = prompt("请输入新的节点名称:", node.data.name);
            if (newName) {
                myDiagram.model.setDataProperty(node.data, "name", newName);
            }
        }

        function editLinkProperties(link) {
            const newLabel = prompt("请输入新的线路标签:", link.data.label);
            if (newLabel) {
                myDiagram.model.setDataProperty(link.data, "label", newLabel);
            }
        }

        function copyNode(node) {
            const newData = Object.assign({}, node.data);
            newData.key = "COPY_" + Date.now();
            newData.name = newData.name + "_副本";
            myDiagram.model.addNodeData(newData);
            showInfo(`已复制节点: ${newData.name}`);
        }

        function splitLink(link) {
            const data = link.data;
            const midNodeKey = "MID_" + Date.now();
            const midNodeData = {
                key: midNodeKey,
                name: "中间节点",
                type: "变电站",
                voltage: "220kV"
            };
            
            myDiagram.model.addNodeData(midNodeData);
            myDiagram.model.addLinkData({ from: data.from, to: midNodeKey, label: data.label + "_1" });
            myDiagram.model.addLinkData({ from: midNodeKey, to: data.to, label: data.label + "_2" });
            myDiagram.model.removeLinkData(data);
            
            showInfo(`线路 ${data.label} 已分割`);
        }

        function addNewNode() {
            const newNodeData = {
                key: "NEW_" + Date.now(),
                name: "新节点",
                type: "变电站",
                voltage: "110kV"
            };
            myDiagram.model.addNodeData(newNodeData);
            showInfo("已添加新节点");
        }

        function exportDiagram() {
            const svg = myDiagram.makeSvg({ scale: 1.0 });
            const blob = new Blob([svg.outerHTML], { type: "image/svg+xml" });
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "电网图.svg";
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearHighlights() {
            myDiagram.startTransaction("clear highlights");
            myDiagram.nodes.each(function(node) {
                myDiagram.model.setDataProperty(node.data, "isHighlighted", false);
            });
            myDiagram.links.each(function(link) {
                myDiagram.model.setDataProperty(link.data, "isHighlighted", false);
            });
            myDiagram.commitTransaction("clear highlights");
        }

        function showAllMenus() {
            alert("右键菜单说明:\n• 节点右键: 显示节点专用菜单\n• 连线右键: 显示线路专用菜单\n• 空白处右键: 显示图表菜单");
        }

        function resetDiagram() {
            myDiagram.model = new go.GraphLinksModel([
                { key: "A", name: "变电站A", type: "变电站", voltage: "500kV" },
                { key: "B", name: "变电站B", type: "变电站", voltage: "220kV" },
                { key: "C", name: "电厂C", type: "电厂", capacity: "1000MW" }
            ], [
                { from: "A", to: "B", label: "线路1", voltage: "220kV", length: "50km" },
                { from: "A", to: "C", label: "线路2", voltage: "500kV", length: "80km" }
            ]);
        }

        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
