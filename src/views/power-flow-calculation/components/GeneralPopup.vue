<template>
	<div class="w-full h-full">
		<button @click="openPopup" class="w-full h-full text-left">{{ params.value }}</button>
		<el-dialog append-to-body v-model="showPopup" :title="title">
			<ag-grid-vue
				class="w-full h-[400px]"
				:columnDefs="columnDefs"
				:theme="myTheme"
				:localeText="localeText"
				rowModelType="serverSide"
				:cacheBlockSize="500"
				:serverSideInitialRowCount="50"
				:suppressServerSideFullWidthLoadingRow="true"
				:getRowId="(params: Record<string, any>) => {
					return params.data.ubid;
				}"
				@GridReady="onGridReady"
				@RowSelected="onRowSelected"
				:defaultColDef="defaultColDef"
				:rowSelection="{ mode: 'singleRow' }"
				:rowData="rowData"
			/>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closePopup">取消</el-button>
					<el-button type="primary" @click="submitPopup">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { computed, ref, defineComponent, inject, PropType, type Ref, type ComputedRef } from 'vue';
import { AgGridVue } from 'ag-grid-vue3';
import { type GridApi, type ICellRendererParams, type ColDef } from 'ag-grid-community';
import { ModuleRegistry } from 'ag-grid-community';
import { FiltersToolPanelModule, ServerSideRowModelApiModule, ServerSideRowModelModule, RowSelectionModule } from 'ag-grid-enterprise';
ModuleRegistry.registerModules([FiltersToolPanelModule, ServerSideRowModelApiModule, ServerSideRowModelModule, RowSelectionModule]);
import { myTheme, localeText, defaultColDef } from '/@/config/ag-grid';
import { configColumns, dataApiConfig } from '/@/config/SimulationConfig';
import { request } from '/@/utils/service';
interface CellRendererParams extends ICellRendererParams {
	colDef: {
		cellRendererParams: {
			table_key: string;
			valueFields: Record<string, string>;
		};
		headerName?: string;
	};
}

interface SetupReturn {
	showPopup: Ref<boolean>;
	rowData: Ref<Recordable[]>;
	columnDefs: Ref<ColDef[]>;
	localeText: typeof localeText;
	myTheme: typeof myTheme;
	defaultColDef: typeof defaultColDef;
	title: ComputedRef<string>;
	openPopup: () => void;
	closePopup: () => void;
	submitPopup: () => void;
	onRowSelected: (params: { data: Recordable }) => void;
	onGridReady: (params: { api: GridApi }) => void;
}

export default defineComponent({
	name: 'GeneralPopup',
	components: {
		AgGridVue,
	},
	props: {
		params: {
			type: Object as PropType<CellRendererParams>,
			required: true,
		},
	},
	setup(props): SetupReturn {
		const showPopup = ref(false);
		const rowData = ref<Recordable[]>([]);
		const selectedRows = ref<Recordable>({});
		const gridApi = ref<GridApi>();
		const loading = ref(false);

		const { table_key, valueFields } = props.params.colDef.cellRendererParams;

		const columnDefs = ref<ColDef[]>(
			configColumns[table_key as keyof typeof configColumns]?.map((item: ColDef) => {
				if (item.filter === 'agSetColumnFilter') {
					return {
						...item,
						editable: false,
						cellEditor: null,
					};
				}
				return {
					headerName: item.headerName,
					field: item.field,
					filter: item.filter,
					hide: item.hide || undefined,
					editable: false,
				};
			}) || []
		);

		const title = computed(() => `选择${props.params?.colDef?.headerName || ''}`);

		const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;

		const dataSource = {
			getRows: async (gridParams: Record<string, any>) => {
				const { startRow, endRow, filterModel, sortModel } = gridParams.request;
				const pageSize = endRow - startRow;
				const page = startRow / pageSize + 1;

				try {
					const response = await request({
						url: dataApiConfig[table_key as keyof typeof dataApiConfig].list,
						method: 'post',
						data: {
							page: page,
							limit: pageSize,
							filterModel: {
								...filterModel,
								bb_case_id: {
									filterType: 'number',
									type: 'exact',
									filter: `${dataPacket.value.id}`,
								},
							},
							sortModel,
						},
					});
					const { code, data } = response;
					if (code === 2000) {
						page === 1 ? (rowData.value = data) : rowData.value.push(...data);
						gridParams.success({ rowData: data });
					} else {
						gridParams.fail();
					}
				} catch (error) {
					gridParams.fail();
				} finally {
					loading.value = false;
				}
			},
		};

		const openPopup = () => {
			showPopup.value = true;
		};

		const closePopup = () => {
			showPopup.value = false;
		};

		// const getValue = () => {
		// 	const rowData = props.params.data;
		// 	return selectedRows.value?.[valueField] || rowData[valueField];
		// };

		const submitPopup = () => {
			const rowData = props.params.data;
			Object.entries(valueFields).forEach(([key, value]) => {
				rowData[key] = selectedRows.value[value];
			});
			props.params.api.applyServerSideTransaction({ update: [rowData] });

			if (props.params.context?.onCellValueChanged) {
				props.params.context.onCellValueChanged({
					data: rowData,
					colDef: props.params.colDef,
				});
			}

			closePopup();
		};

		const onRowSelected = ({ data }: { data: Recordable }) => {
			selectedRows.value = data;
		};

		const onGridReady = (params: { api: GridApi }) => {
			gridApi.value = params.api;
			gridApi.value!.setGridOption('serverSideDatasource', dataSource);
		};

		return {
			showPopup,
			rowData,
			columnDefs,
			localeText,
			myTheme,
			defaultColDef,
			title,
			openPopup,
			closePopup,
			// getValue,
			submitPopup,
			onRowSelected,
			onGridReady,
		};
	},
});
</script>
