<template>
	<div class="bg-[#f7f7f7] h-full flex flex-col overflow-hidden dispatcher-power-flow-content">
		<el-button-group class="operatingSteps">
			<el-button type="primary" :icon="Help" @click="openOperatingSteps">操作步骤</el-button>
			<el-button type="primary" :icon="Connection" @click="openTransferRatio">转移比</el-button>
			<el-button type="primary" :icon="Aim">计算</el-button>
		</el-button-group>
		<SubstationGraph :lineOperation=true></SubstationGraph>
		<!-- 操作步骤 -->
		<operatingSteps ref="operatingStepsRef"></operatingSteps>
		<!-- 转移比 -->
		 <transferRatio ref="transferRatioRef"></transferRatio>
	</div>
</template>

<script setup lang="ts">
    import { ref, computed } from 'vue';
	import SubstationGraph from '../graph/substation-2/index.vue';
	import { Help, Connection, Aim} from '@element-plus/icons-vue'
	import operatingSteps from './operatingSteps.vue'
	import transferRatio from './transferRatio.vue'
	const operatingStepsRef = ref()
	const transferRatioRef = ref()
	const openOperatingSteps = () => {
		operatingStepsRef.value.init()
	}
	const openTransferRatio = () => {
		transferRatioRef.value.init()
	}
</script>
<style scoped lang="scss">
.dispatcher-power-flow-content{
	position: relative;
.operatingSteps{
	position: absolute;
	right: 20px;
	top: 20px;
	z-index: 999;
}
}
</style>