// 电厂站节点数据接口
interface SubstationNode {
	key: string; // 节点唯一标识，对应后端的id或graph_node_id
	name: string; // 节点名称，对应后端的label
	type: string; // 节点类型名称
	category: 'station' | 'plant'; // 节点类型
	voltage?: string; // 电压等级，对应后端的basevoltage_name
	x?: number; // x坐标
	y?: number; // y坐标
	color: string;
	properties?: SubstationNodeProperties; // 用于显示和 TextBlock
}

// 电厂站连接线数据接口
interface SubstationLink {
	name: string; // 线路名称
	from: string; // 起点节点id
	source: string; // 起点节点id
	to: string; // 终点节点id
	target: string; // 终点节点id
	color: string; // 线条颜色
	direction: string; // 方向
	voltage: string; // 电压等级
	lineCount: number; // 线路数量
	lineIndex: number; // 当前线路索引
	points?: Point[]; // 路径点数组，用于绘制弯曲线路
	properties?: SubstationLinkProperties;
}
// 电厂站连接线属性数据接口
interface SubstationLinkProperties {
	max_i_ka: number;
	p_from_mw: number;
	q_from_mvar: number;
	p_to_mw: number;
	q_to_mva: number;
	pl_mw: number;
	ql_mvar: number;
	i_from_ka: number;
	i_to_ka: number;
	i_ka: number;
	vm_from_pu: number;
	va_from_degree: number;
	vm_to_pu: number;
	va_to_degree: number;
	loading_percent: number;
}

interface SubstationNodeProperties {
	U: number;
	'U(M)': number;
}

// 坐标点接口
interface Point {
	x: number;
	y: number;
}

interface SingleLineNode {
	key: string;
	category: string;
	name: string;
	type: string;
	color: string;
	pos: [number, number];
	posX?: number;
	posY?: number;
	angle: number;
	width?: number;
	isTemplate?: boolean;
	[key: string]: any;
}

interface PaletteDroppedEvent {
	nodeData: SingleLineNode;
	position: go.Point;
}

interface SingleLineLink {
	key?: string; // 连线唯一标识
	from: string; // 起点节点ID
	to: string; // 终点节点ID
	fromPort?: string; // 起点端口ID
	toPort?: string; // 终点端口ID
	points?: go.Point[]; // 路径点
	voltage?: string; // 电压等级
	color?: string; // 线路颜色
	properties?: any; // 连线属性数据
	[key: string]: any; // 允许其他扩展属性
}

// 定义端口信息
interface PortInfo {
	id: string; // 端口ID
	alignment: go.Spot; // 端口位置
	fromSpot?: go.Spot; // 连出方向
	toSpot?: go.Spot; // 连入方向
	fromLinkable?: boolean; // 是否可以作为连出端口
	toLinkable?: boolean; // 是否可以作为连入端口
	fromMaxLinks?: number; // 最大可连出线数量
	toMaxLinks?: number; // 最大可连入线数量
}

interface IconInfo {
	path: string;
	width: number;
	height: number;
	name: string;
	type: string;
	strokeWidth: number;
	defaultColor: string;
	ports?: PortInfo[]; // 添加端口配置
}

interface IconsCollection {
	[key: string]: IconInfo;
}

// 圆环配置接口
interface CircleConfig {
	diameter: number;
	stroke: string;
	strokeWidth: number;
}

// 电压等级配置接口
interface VoltageConfig {
	name: string; // 电压等级名称
	key: string; // 搜索用的关键字
	color: string; // 主要颜色
	circles: CircleConfig[]; // 圆环配置
	lineWidth?: number; // 连接线宽度
}

// 平行线路配置
interface ParallelLineConfig {
	segmentFraction: number; // 线上元素位置比例
}

// 平行线路配置对象
interface ParallelLinesConfig {
	[lineCount: number]: {
		// 线路总数
		[lineIndex: number]: ParallelLineConfig; // 每条线的配置
	};
}

// 端口配置接口 - 扩展现有的PortInfo接口
interface PortConfig {
	id: string;
	alignment: go.Spot;
	fromSpot?: go.Spot;
	toSpot?: go.Spot;
	fromLinkable?: boolean;
	toLinkable?: boolean;
	fromMaxLinks?: number;
	toMaxLinks?: number;
}

// 组件图标接口 - 扩展现有的IconInfo接口
interface ComponentIcon {
	type: string;
	name: string;
	path: string;
	defaultColor: string;
	strokeWidth: number;
	width: number;
	height: number;
	ports?: PortConfig[];
}

// 右键菜单项接口
interface ContextMenuItem {
	text: string;
	icon?: string;
	action: (e: go.InputEvent, obj: go.GraphObject) => void;
	disabled?: (e: go.InputEvent, obj: go.GraphObject) => boolean;
	divider?: boolean;
}

// 节点模板配置接口
interface NodeTemplateConfig {
	type: string;
	template: go.Node;
	contextMenuItems?: ContextMenuItem[];
}

// 电压等级样式接口
interface VoltageStyle {
	color: string;
	strokeWidth: number;
}
