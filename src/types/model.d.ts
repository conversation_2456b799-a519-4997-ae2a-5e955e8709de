// 导航配置类型定义
interface NavDropdownItem {
	key: string;
	label: string;
	icon?: string;
	iconSize?: number;
	type?: 'page' | 'action';
}

interface NavSubItem {
	key: string;
	label: string;
	icon: string;
	iconSize?: number;
	hasDropdown?: boolean; // 是否有下拉菜单
	dropdownItems?: NavDropdownItem[]; // 下拉菜单项
}

interface NavItem {
	key: string;
	label: string;
	icon?: string;
	iconSize?: number;
	type?: 'page' | 'action'; // page: 页面切换, action: 特殊行为
	subItems?: NavSubItem[]; // 复合按钮的子项
}

interface NavGroup {
	title?: string;
	type: 'simple' | 'composite'; // simple: 普通按钮组, composite: 复合按钮组
	items: NavItem[];
}
