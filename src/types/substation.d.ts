// 电厂站节点数据接口
interface SubstationNode {
	key: string; // 节点唯一标识，对应后端的id或graph_node_id
	name: string; // 节点名称，对应后端的label
	type: string; // 节点类型名称
	category: 'station' | 'plant'; // 节点类型
	voltage?: string; // 电压等级，对应后端的basevoltage_name
	x?: number; // x坐标
	y?: number; // y坐标
	color: string;
	properties?: SubstationNodeProperties; // 用于显示和 TextBlock
}

// 电厂站节点属性数据接口
interface SubstationNodeProperties {
	U: number;
	'U(M)': number;
}

// 电厂站连接线数据接口
interface SubstationLink {
	name: string; // 线路名称
	from: string; // 起点节点id
	source: string; // 起点节点id
	to: string; // 终点节点id
	target: string; // 终点节点id
	color: string; // 线条颜色
	direction: string; // 方向
	voltage: string; // 电压等级
	lineCount: number; // 线路数量
	lineIndex: number; // 当前线路索引
	points?: Point[]; // 路径点数组，用于绘制弯曲线路
	properties?: SubstationLinkProperties;
}
// 电厂站连接线属性数据接口
interface SubstationLinkProperties {
	max_i_ka: number;
	p_from_mw: number;
	q_from_mvar: number;
	p_to_mw: number;
	q_to_mva: number;
	pl_mw: number;
	ql_mvar: number;
	i_from_ka: number;
	i_to_ka: number;
	i_ka: number;
	vm_from_pu: number;
	va_from_degree: number;
	vm_to_pu: number;
	va_to_degree: number;
	loading_percent: number;
}

// 节点搜索选项接口
interface NodeSearchOption {
	key: string;
	label: string;
	type: string;
}

// 线路指标配置接口
interface LineIndicatorConfig {
	showLineName: boolean;
	showP: boolean;
	showPQ: boolean;
	showLoading: boolean;
	showPM: boolean;
	showPQM: boolean;
	showPDelta: boolean;
	showPQDelta: boolean;
}
